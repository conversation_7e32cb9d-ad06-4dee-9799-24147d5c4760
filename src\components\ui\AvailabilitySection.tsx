import { Card, CardBody, CardHeader, Checkbox, TimeInput } from "@heroui/react";
import CustomDivider from "./CustomDivider";
import CustomPoporver from "./CustomPoporver";
import { IoMdTime } from "react-icons/io";
import CustomNumberInput from "./CustomNumberInput";
import { Controller, useFormContext } from "react-hook-form";
import { IServiceSubmitProps } from "../../types";
import {
  convertToInternationalizedTimeToReadble,
  stringTimeConvertToHHMM,
} from "../../utils/convertTime";
import { Time, TimeValue } from "@internationalized/date";
import { firstLetterUpperCase } from "../../utils/common";

const daysOfWeek = [
  "sunday",
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
];

const AvailabilitySection = () => {
  const {
    control,
    watch,
    formState: { errors },
  } = useFormContext<IServiceSubmitProps>();

  {
    /* <CardHeader>
    <div className="flex items-center justify-between gap-3">
      <p className="text-md font-medium">Availability</p>
      <CustomPoporver content={poporverContent} />
    </div>
  </CardHeader> */
  }
  return (
    <div>
      <table className=" w-full border-collapse border border-gray-200 dark:border-gray-700 text-body">
        <thead className="">
          <tr>
            <th className="border-1 border-gray-200 dark:border-gray-700 px-3 py-2 text-left text-body-bold">
              Working days
            </th>
            <th className="border-1 border-gray-200 dark:border-gray-700 px-3 py-2 text-center  text-body-bold">
              Time (Start - End)
            </th>
            <th className="border-1 border-gray-200 dark:border-gray-700 px-3 py-2 text-center  text-body-bold">
              Max. Appointments/Day
            </th>
          </tr>
        </thead>
        <tbody>
          {daysOfWeek.map((day, index) => (
            <tr key={day}>
              <td className="border-1 border-gray-200  dark:border-gray-700 px-3">
                <Controller
                  name={`availability.${index}.day`}
                  control={control}
                  defaultValue={""}
                  render={({ field }) => (
                    <Checkbox
                      isSelected={field.value === day}
                      onChange={
                        () => field.onChange(field.value === day ? "" : day) // toggle on/off
                      }
                      value={day}
                      classNames={{
                        label: "text-body",
                      }}
                    >
                      {firstLetterUpperCase(day)}
                    </Checkbox>
                  )}
                />
              </td>

              {/* Time slots */}
              <td className="flex gap-3 justify-center items-baseline border-0.1 border-b border-gray-200 dark:border-gray-700  px-3 py-2">
                {!watch(`availability.${index}.day`) ? (
                  <div className="px-3 py-2.5 ">
                    <p className="italic text-gray-400">Holiday</p>
                  </div>
                ) : (
                  <>
                    <Controller
                      name={`availability.${index}.timeSlots.from`}
                      control={control}
                      render={({ field }) => (
                        <TimeInput
                          labelPlacement="outside"
                          className="min-w-[100px]"
                          variant="bordered"
                          value={
                            field.value
                              ? new Time(
                                  stringTimeConvertToHHMM(field.value).hour,
                                  stringTimeConvertToHHMM(field.value).minute
                                )
                              : undefined
                          }
                          startContent={<IoMdTime />}
                          onChange={(val: TimeValue) => {
                            const convertTime =
                              convertToInternationalizedTimeToReadble(val);
                            field.onChange(convertTime);
                          }}
                          isInvalid={
                            !!errors?.availability?.[index]?.timeSlots?.from
                          }
                          errorMessage={
                            errors?.availability?.[index]?.timeSlots?.from
                              ?.message
                          }
                          isDisabled={!watch(`availability.${index}.day`)}
                        />
                      )}
                    />

                    <p className="py-2 font-semibold">-</p>

                    <Controller
                      name={`availability.${index}.timeSlots.to`}
                      control={control}
                      render={({ field }) => (
                        <TimeInput
                          labelPlacement="outside"
                          className="min-w-[100px]"
                          variant="bordered"
                          startContent={<IoMdTime />}
                          value={
                            field.value
                              ? new Time(
                                  stringTimeConvertToHHMM(field.value).hour,
                                  stringTimeConvertToHHMM(field.value).minute
                                )
                              : undefined
                          }
                          onChange={(val: TimeValue) => {
                            const convertTime =
                              convertToInternationalizedTimeToReadble(val);
                            field.onChange(convertTime);
                          }}
                          isInvalid={
                            !!errors?.availability?.[index]?.timeSlots?.to
                          }
                          errorMessage={
                            errors?.availability?.[index]?.timeSlots?.to
                              ?.message
                          }
                          isDisabled={!watch(`availability.${index}.day`)}
                        />
                      )}
                    />
                  </>
                )}
              </td>

              {/* Max slots */}
              <td className="border border-gray-200 dark:border-gray-700 px-3 py-2  ">
                {!watch(`availability.${index}.day`) ? (
                  <div className="px-3 py-2.5 flex justify-center items-center">
                    <p className="italic text-gray-400">Holiday</p>
                  </div>
                ) : (
                  <div className=" flex justify-center items-center">
                    <Controller
                      name={`availability.${index}.timeSlots.maxBookings`}
                      control={control}
                      render={({ field }) => (
                        <CustomNumberInput
                          {...field}
                          placeholder="Enter slot"
                          className="max-w-[150px]"
                          isInvalid={
                            !!errors?.availability?.[index]?.timeSlots
                              ?.maxBookings
                          }
                          errorMessage={
                            errors?.availability?.[index]?.timeSlots
                              ?.maxBookings?.message
                          }
                          isDisabled={!watch(`availability.${index}.day`)}
                        />
                      )}
                    />
                  </div>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
    // </Card>
  );
};

export default AvailabilitySection;

const poporverContent = () => (
  <div className="text-sm space-y-2">
    <div className="font-semibold text-base text-gray-800 dark:text-white">
      Availability Preview
    </div>
    <div className="bg-gray-50 dark:bg-sideBarBackground dark:border-gray-600 p-2 rounded-md border text-gray-700 dark:text-gray-100 space-y-1">
      <div>
        <span className="font-medium">Day:</span> Monday
      </div>
      <div>
        <span className="font-medium">Start Time:</span> 1:00 PM
      </div>
      <div>
        <span className="font-medium">End Time:</span> 5:00 PM
      </div>
      <div>
        <span className="font-medium">Max. Appointments/Day:</span> 3
      </div>
    </div>
    <CustomDivider />
    <div className="text-gray-800 font-semibold mb-2 dark:text-gray-100">
      Generated Slots:
    </div>
    <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-200">
      <li>1:00 PM – 2:00 PM</li>
      <li>2:00 PM – 3:00 PM</li>
      <li>4:00 PM – 5:00 PM</li>
    </ul>
    <div className="mt-2 text-xs italic text-gray-500 dark:text-gray-400">
      Note: Gaps may exist due to rest/travel time between slots.
    </div>
  </div>
);
