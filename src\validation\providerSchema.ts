import * as yup from "yup";
import {
  addressRule,
  birthdayRule,
  cityRule,
  countryRule,
  currencyRule,
  documentFileRule,
  documentNoRule,
  documentVerifyTypeRule,
  languageRule,
  nameRule,
  phoneNoRule,
  pincodeRule,
  profileImageRule,
  provideBio,
  stateRule,
} from "./ValidationRules";

export const providerSchema = yup.object().shape({
  // name: nameRule,
  // mobile: phoneNoRule,
  dateOfBirth: birthdayRule,
  bio: provideBio,
  // profilePicture: profileImageRule,
  address: yup.object().shape({
    addressLine1: addressRule,
    country: countryRule,
    state: stateRule,
    city: cityRule,
    postalCode: pincodeRule,
  }),
  // language: languageRule,
  // currencyCode: currencyRule,
  // status: false,
  providerStatus: yup.string().oneOf(["approved"]),
  groupRole: yup.string().oneOf(["provider"]),
  documents: yup.object().shape({
    // documentNo: documentNoRule,
    // type: documentVerifyTypeRule,
    // front: documentFileRule,
    // back: documentFileRule,
  }),
});
