# 🚀 Provider Dashboard

A **React + TypeScript** dashboard built with **Vite** and **Yarn**, based on gigmosaic-provider-dashboard.

## 📌 Features
- ⚡ **Vite** for blazing-fast development  
- 🔷 **TypeScript** for type safety  
- 🎨 **TailwindCSS** for styling  
- 🔄 **React Router** for navigation  
- 🛠 **ESLint & Prettier** for code consistency
- 🎨 **Hero Ui** for styling and prebuild components 

---

## 📥 Installation

### 1️⃣ Clone the Repository  

```https://github.com/aplicy-com/gigmosaic-provider-dashboard.git```

### 2️⃣ Install Dependencies

```yarn install```

## 🚀 Running the Project

### ▶️ Start Development Server

```yarn dev```

The app will be available at ```http://localhost:5173/```

### 🔨 Build for Production

```yarn build```

### ✅ Lint & Format Code

```
yarn lint
yarn format
```

## 📂 Project Structure

```
providerdashboard/
│── src/                # Source code  
│   ├── components/     # Reusable UI components  
│   ├── pages/          # Page components  
│   ├── layout/         # Layout components  
│   ├── assets/         # Static assets (images, icons)  
│   ├── routes.tsx      # Route definitions  
│   ├── main.tsx        # Entry point  
│   ├── App.tsx         # Main App component  
│── public/             # Static files  
│── .gitignore          # Ignored files  
│── tsconfig.json       # TypeScript config  
│── tailwind.config.js  # TailwindCSS config  
│── vite.config.ts      # Vite config  
│── package.json        # Project metadata  
│── README.md           # Project documentation
 ```

## 🛠 Technologies Used

- React.js (with Vite)

- TypeScript

- TailwindCSS

- React Router

- ESLint & Prettier

## 📌 Environment Variables

Create a ``.env`` file in the root directory and add necessary variables:

``VITE_API_URL=https://your-api.com``

## 🤝 Contributing

Fork the repository

Clone your fork

``git clone https://github.com/aplicy-com/gigmosaic-provider-dashboard.git``

Create a new branch

``git checkout -b feature-name``

Commit changes

``git commit -m "Add new feature"``

Push and create a pull request

``git push origin feature-name``

## 📜 License

This project is licensed under the MIT License.

## ⭐ Show Your Support

If you like this project, please ⭐ the repository! 🚀
