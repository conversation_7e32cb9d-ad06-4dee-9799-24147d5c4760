import ThemeSwitcher from "../ThemeSwitcher";
import Breadcrumb from "./Breadcrumb";
import LanguageSelect from "./LanguageSelect";
import ProfileDropdown from "./ProfileDropdown";
import { TbWorld } from "react-icons/tb";
import NotificationDrawer from "./NotificationDrawer";
import Logo from "../../assets/logo-small.png";
import LargeLogo from "../../assets/Logo.png";
import { Link } from "react-router-dom";
import { HiOutlineMenuAlt1 } from "react-icons/hi";

const Header = () => {
  return (
    <>
      {/* <header className="fixed top-0 right-0 w-full shadow-sm dark:bg-[#161a20] bg-[#ffffff]  px-4 z-30"> */}
      {/* <header className="fixed top-0 right-0 w-full shadow-sm bg-white/5 backdrop-blur-3xl px-4 z-30 dark:bg-gray-900/30"> */}
      <header className="sticky top-0 right-0 px-4 z-30 border-b border-border bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
        <div className="header-content flex items-center flex-row my-3">
          <div className="hidden md:block md:ml-[250px]">
            <Breadcrumb />
          </div>

          <div className="flex ml-auto mr-3 justify-center items-center">
            <div className="flex flex-initial justify-center items-center mr-4">
              <ThemeSwitcher />
            </div>
            <a
              href="https://www.int.gigmosaic.ca/"
              target="_blank"
              rel="noopener noreferrer"
            >
              <div className="flex flex-initial  justify-center items-center mr-3 cursor-pointer">
                <TbWorld
                  size={25}
                  className="text-gray-500 hover:text-gray-800 dark:hover:text-white transition-colors duration-300"
                />
                <p className="hover:underline hover:text-primary-700 ml-1 cursor-pointe text-sm">
                  Visit site
                </p>
              </div>
            </a>

            <div className=" mr-3">
              <LanguageSelect />
            </div>
            <NotificationDrawer />
            <ProfileDropdown />
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;

// <header className="fixed top-0 right-0 w-full shadow-sm dark:bg-[#161a20] bg-[#ffffff]  px-4 z-30">
//       <div className="flex ml-auto  justify-between items-center my-2.5">
//         <div className="laptop:hidden">
//           {/* <Link to={"/dashboard"}>
//             <img src={Logo} alt="Logo" className="w-10 h-10" />
//           </Link> */}
//           <HiOutlineMenuAlt1 size={28} />
//         </div>

//         {/* <div className="hidden laptop:block">
//           <Link to={"/dashboard"}>
//             <img src={LargeLogo} alt="Logo" />
//           </Link>
//         </div> */}
//         <ThemeSwitcher />

//         <div className="flex items-center">
//           <div className=" mr-2">
//             <LanguageSelect />
//             {/* <ThemeSwitcher /> */}
//           </div>
//           <NotificationDrawer />
//           <ProfileDropdown />
//         </div>
//       </div>
//     </header>
