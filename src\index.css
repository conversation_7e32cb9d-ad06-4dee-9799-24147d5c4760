@tailwind base;
@tailwind components;
@tailwind utilities;
/* @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"); */

/* HirePanther Provider Portal Design System */

@layer base {
  :root {
    /* Core Colors - Company Brand Theme */
    --background: 0 0% 100%;
    --foreground: 208 47% 25%;

    --card: 0 0% 100%;
    --card-foreground: 208 47% 25%;

    --popover: 0 0% 100%;
    --popover-foreground: 208 47% 25%;

    /* Primary Brand Colors - HEX #317690 */
    --primary: 198 49% 38%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 198 49% 33%;
    --primary-light: 198 49% 95%;

    /* Secondary Brand Colors - HEX #224860 */
    /* --secondary: 208 47% 25%; */
    --secondary: 196 100% 38%;
    /* --secondary: #0ca7c1; */
    --secondary-foreground: 0 0% 100%;

    /* Brand Secondary Palette */
    --brand-cyan: 188 88% 40%; /* #0CA7C1 */
    --brand-turquoise: 183 100% 42%; /* #00CED8 */
    --brand-light-cyan: 183 57% 59%; /* #5DCBD1 */
    --brand-pale-cyan: 185 73% 76%; /* #95E7EF */
    --brand-dark-blue: 198 41% 27%; /* #284F60 */
    --brand-navy: 200 51% 18%; /* #163544 */
    --brand-steel: 199 46% 33%; /* #2D617A */
    --brand-ocean: 194 65% 36%; /* #207D99 */

    /* Accent - Light Tint */
    --accent: 198 49% 96%;
    --accent-foreground: 208 47% 25%;

    /* Muted - Neutral Gray */
    --muted: 250 15% 96%;
    --muted-foreground: 240 10% 50%;

    /* Destructive - Warm Red */
    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Success - Fresh Green */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 71% 95%;

    /* Warning - Amber */
    --warning: 43 96% 56%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 43 96% 95%;

    /* Borders & Inputs */
    --border: 250 20% 88%;
    --input: 250 20% 88%;
    --ring: 257 87% 65%;

    /* Gradients */
    --gradient-primary: linear-gradient(
      135deg,
      oklab(52.81400000000001% -0.05294 -0.06022),
      hsl(198 49% 45%)
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(208 47% 25%),
      hsl(208 47% 20%)
    );
    --gradient-accent: linear-gradient(
      135deg,
      hsl(198 49% 96%),
      hsl(0 0% 100%)
    );

    /* Shadows */
    --shadow-primary: 0 4px 20px -4px hsl(198 49% 38% / 0.3);
    --shadow-card: 0 2px 10px -2px hsl(208 47% 25% / 0.1);
    --shadow-elevated: 0 8px 30px -8px hsl(208 47% 25% / 0.15);

    /* Layout */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;

    /* Sidebar - Dark Theme */
    --sidebar-background: 0 0% 8%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 198 49% 38%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 12%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 198 49% 38%;

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@layer base {
  /* * {
    @apply border-border;
  } */

  /* body {
    @apply bg-background text-foreground font-primary antialiased;
  } */

  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}

@layer components {
  /* Gradient Backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-accent {
    background: var(--gradient-accent);
  }

  /* Shadow Utilities */
  .shadow-primary {
    box-shadow: var(--shadow-primary);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-elevated {
    box-shadow: var(--shadow-elevated);
  }

  /* Animation Classes */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.4s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Interactive Elements */
  .hover-lift {
    transition: var(--transition-smooth);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-elevated);
  }
}

@layer utilities {
  /* Text */
  .text-heading {
    @apply text-3xl font-bold;
    color: hsl(var(--foreground));
  }
  .text-title {
    @apply text-medium font-medium;
    color: hsl(var(--foreground));
  }

  .text-body {
    @apply text-sm;
    color: hsl(var(--foreground));
    font-family: var(--primary);
  }

  .text-body-bold {
    @apply text-sm font-semibold;
    color: hsl(var(--foreground));
    font-family: var(--primary);
  }

  .text-body-mute {
    color: hsl(var(--muted-foreground));
  }

  .text-caption {
    @apply text-xs;
    color: hsl(var(--muted-foreground));
  }

  .text-sidebar {
    @apply text-sm font-medium text-gray-200;
    font-family: var(--primary);
  }

  .text-active {
    @apply text-sm font-bold;
    color: hsl(var(--secondary));
  }

  .text-link {
    color: hsl(var(--link));
  }

  .text-link:hover {
    color: hsl(var(--link-hover));
  }

  /* Status Colors */
  .text-success {
    @apply text-sm;
    color: hsl(var(--success));
  }

  .text-warning {
    @apply text-sm;
    color: hsl(var(--warning));
  }

  .text-danger {
    @apply text-sm text-red-400;
  }

  .bg-success {
    background-color: hsl(var(--success));
  }

  .bg-warning {
    background-color: hsl(var(--warning));
  }

  .bg-success-light {
    background-color: hsl(var(--success-light));
  }

  .bg-warning-light {
    background-color: hsl(var(--warning-light));
  }

  .bg-primary-light {
    background-color: hsl(var(--primary-light));
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* BORDER */
  .primary-border {
    @apply border-1 border-secondary/70;
  }

  /* HOVER COLOR */
  .hover-primary-gradient:hover {
    background: linear-gradient(
      90deg,
      hsla(196, 85%, 95%, 1) 0%,
      hsla(0, 0%, 100%, 1) 100%
    );
    transition: all 2.3s ease-in-out;
  }

  .primary-gradient {
    background: linear-gradient(
      90deg,
      hsla(196, 85%, 95%, 1) 0%,
      hsla(0, 0%, 100%, 1) 100%
    );
    transition: all 2.3s ease-in-out;
  }

  .hover-round-effect {
    @apply relative flex  items-center justify-center overflow-hidden transition-all hover:scale-105 before:absolute before:h-0 before:w-0 before:rounded-full before:bg-secondary/10 before:duration-300 before:ease-out hover:shadow-md hover:before:h-56 hover:before:w-56;
  }

  .my-booked-class {
    @apply bg-secondary rounded-full text-white;
  }

  .calendar-holiday {
    @apply bg-red-400 rounded-full text-white;
  }

  .calendar-today {
    @apply text-blue-500 font-bold;
  }
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
