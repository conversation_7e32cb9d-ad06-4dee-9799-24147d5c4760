import CustomInput from "../../components/ui/CustomInput";
import { useMemo, useState } from "react";
import AddDiscountModal from "../../components/ui/AddDiscountModal";
import PageHeader from "../../components/ui/PageHeader";
import TabNav from "../../components/ui/TabNav";
import CustomCardWithHeader from "../../components/ui/CustomCardWithHeader";

import StatsCard from "../../components/ui/StatsCard";
import { MdDiscount } from "react-icons/md";
import { FaChartLine, FaDollarSign } from "react-icons/fa";
import CustomProgressBar from "../../components/ui/CustomProgressBar";
import {
  useFetchDiscountById,
  useFetchPromotions,
} from "../../hooks/queries/useFetchData";
import { IPromotionGet } from "../../types";
import { firstLetterUpperCase } from "../../utils/common";
import moment from "moment";
import CustomPagination from "../../components/ui/CustomPagination";
import CustomButton from "../../components/ui/CustomButton";
import SmallLoadingSpinner from "../../components/ui/SmallLoadingSpinner";
import NoDataFound from "../NoDataFound";

const AllPromotion = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchId, setSearchId] = useState<string>("");
  const [searchInput, setSearchInput] = useState<string>("");

  const { data, isFetching } = useFetchPromotions({
    page: currentPage,
    limit: 10,
  });
  const {
    data: discountData,
    isFetching: isSearching,
    isError: isSearchError,
  } = useFetchDiscountById(searchId);

  let promotions: IPromotionGet[];

  // const promotions: IPromotionGet[] = data?.discounts || [];

  const totalPage = useMemo(() => data?.pages || 1, [data]);

  if (discountData?.discount) {
    promotions = [discountData.discount];
  } else {
    promotions = data?.discounts || [];
  }

  console.log("all_promotion: ", promotions);
  console.log("discountData: ", discountData);

  const tabs = [
    { id: "all", title: "All Promotions" },
    { id: "active", title: "Active Promotions" },
    { id: "complete", title: "Complete Promotions" },
    { id: "analytics", title: "Analytics" },
  ];

  const handleSearch = () => setSearchId(searchInput);
  const clearSearch = () => setSearchId("");

  const handleProgressBarColor = (maxValue: number, usage: number) => {
    if (!maxValue && !usage) return "primary";
    const value = Number(((Number(usage) / Number(maxValue)) * 100).toFixed(2));

    console.log("Value: ", value);

    if (value <= 50) return "success"; // below 50%
    if (value >= 50 && value < 70) return "warning"; // 50% - 69.99%
    if (value >= 70) return "danger"; // 70% - 100%

    return "primary"; // fallback
  };

  return (
    <>
      {/*VERIFY ALERT  */}
      {/* {!isVerifyAccount && (
        <div className="flex items-center justify-center w-full mb-3 -mt-1">
          <Alert
            variant="faded"
            color="warning"
            description={description}
            title={title}
            endContent={
              <CustomButton
                onPress={() => navigate("/user/profile/security")}
                color="warning"
                size="sm"
                variant="flat"
                label="Verify Account"
              />
            }
          />
        </div>
      )} */}
      <PageHeader
        title="Promotions & Discounts"
        description="Create and manage promotional campaigns to attract customers"
        components={
          <div className="flex gap-5">
            <CustomInput
              isClearable
              placeholder="Search by discount ID"
              type="text"
              size="sm"
              className="md:w-[350px]"
              onValueChange={(e) => setSearchInput(e)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSearch();
                }
              }}
              onClear={() => clearSearch()}
            />

            <CustomButton
              label="Search"
              className="ml-2"
              color="secondary"
              onPress={handleSearch}
              isLoading={isSearching}
            />

            <div>
              <AddDiscountModal />
            </div>
          </div>
        }
      />

      <div className="grid tablet:grid-cols-2 laptop:grid-cols-4 gap-5">
        <StatsCard
          title="All Promotions"
          isHoverEffect={true}
          value={promotions?.length || "-"}
          icon={<MdDiscount size={30} className=" text-secondary" />}
        />
        <StatsCard
          title="Active Promotions"
          isHoverEffect={true}
          value={28}
          icon={<MdDiscount size={30} className=" text-secondary" />}
        />
        <StatsCard
          title="Revenue with Promotions"
          isHoverEffect={true}
          value={"$1200"}
          icon={<FaDollarSign size={30} className=" text-secondary" />}
        />
        <StatsCard
          title="ROI"
          isHoverEffect={true}
          value={"28%"}
          icon={<FaChartLine size={30} className=" text-secondary" />}
        />
      </div>

      <TabNav activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />

      {activeTab === "all" && (
        <div className="grid mt-5 gap-4">
          {isFetching || isSearching ? (
            <SmallLoadingSpinner size="md" variant="wave" />
          ) : isSearchError || promotions.length === 0 ? (
            <NoDataFound isIcon />
          ) : (
            <>
              {promotions?.map((p: IPromotionGet, index: number) => (
                <CustomCardWithHeader
                  key={index}
                  title={p?.serviceName}
                  isChip={true}
                  chipText="Active"
                  chipColor="success"
                  isSecondChip={p?.packageName ? true : false}
                  secondChipColor="secondary"
                  secondChipText={p?.packageName && p?.packageName}
                  className="hover:bg-secondary/5 transition-all duration-300 hover:shadow-md"
                  mainContent={
                    <div className="-mt-4">
                      <div className="grid grid-cols-2 tablet:grid-cols-4 laptop:grid-cols-8 text-body gap-4">
                        <div className="flex flex-col">
                          <p className="text-body">Discount Id</p>
                          <p className="text-body-bold">
                            {p?.discountId || "-"}
                          </p>
                        </div>
                        <div className="flex flex-col ">
                          <p className="text-body">Discount Type</p>
                          <p className="text-body-bold font-primary">
                            {firstLetterUpperCase(p?.discountType) || "-"}
                          </p>
                        </div>

                        <div className="flex flex-col ">
                          <p className="text-body">Value Type</p>
                          <p className="text-body-bold font-primary">
                            {firstLetterUpperCase(p?.valueType) || "-"}
                          </p>
                        </div>

                        <div className="flex flex-col">
                          <p className="text-body">Value</p>
                          <p className="text-body-bold font-primary">
                            {p?.valueType === "percentage"
                              ? `${p?.amount}%`
                              : `$${p?.amount}`}
                          </p>
                        </div>

                        <div className="flex flex-col">
                          <p className="text-body">Code</p>
                          <p className="text-body-bold">
                            {p?.isPackageDiscount ? "N/A" : p?.promoCode || "-"}
                          </p>
                        </div>

                        <div className="flex flex-col">
                          <p className="text-body">Duration Type</p>
                          <p className="text-body-bold">
                            {p?.durationType === "life-time"
                              ? "Life Time"
                              : "Time Base"}
                          </p>
                        </div>

                        <div className="flex flex-col">
                          <p className="text-body">Duration</p>
                          {p?.durationType === "life-time" ? (
                            <p className="text-body-bold">
                              {moment(p?.duration?.start).format(
                                "DD MMM YYYY"
                              ) || "-"}{" "}
                            </p>
                          ) : (
                            <p className="text-body-bold">
                              {moment(p?.duration?.start).format(
                                "DD MMM YYYY"
                              ) || "-"}{" "}
                              -{" "}
                              {moment(p?.duration?.end).format("DD MMM YYYY") ||
                                "-"}
                            </p>
                          )}
                        </div>

                        <div className="flex flex-col">
                          <p className="text-body">Usage</p>
                          <p className="text-body-bold">
                            {p?.acceptAllUserIds?.length || 0}/
                            {p?.maxCount || 0}
                          </p>
                        </div>
                      </div>

                      <div className="mt-3">
                        <CustomProgressBar
                          lable="Usage Progress"
                          value={p?.acceptAllUserIds?.length || 0}
                          showValueLabel={true}
                          maxValue={p?.maxCount || 0}
                          color={handleProgressBarColor(
                            p?.maxCount,
                            p?.acceptAllUserIds?.length
                          )}
                        />
                      </div>
                    </div>
                  }
                />
              ))}
            </>
          )}
        </div>
      )}

      {activeTab === "analytics" && (
        <div className="grid laptop:grid-cols-2 gap-4 mt-5">
          <CustomCardWithHeader
            title="Top Performing Promotions"
            subtitle="Revenue generated through promotions"
            isHeaderDevider={true}
            className="hover:bg-secondary/5 transition-all duration-300 hover:shadow-md"
            mainContent={
              <div className="space-y-3 -mt-3">
                <div className="flex justify-between items-center">
                  <p className="text-body">Ev Battry Repaire - Pro Package</p>
                  <p className="text-body-bold">124 users</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-body">SUV Battry Replace</p>
                  <p className="text-body-bold">84 users</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-body">Vehical Battry Replace</p>
                  <p className="text-body-bold">62 users</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-body">Ev Battry Repaire - Basic Package</p>
                  <p className="text-body-bold">24 users</p>
                </div>
              </div>
            }
          />

          <CustomCardWithHeader
            title="Promotion Impact"
            subtitle="Revenue generated through promotions"
            isHeaderDevider={true}
            className="hover:bg-secondary/5 transition-all duration-300 hover:shadow-md"
            mainContent={
              <div className="space-y-3 -mt-3">
                <div className="flex justify-between items-center">
                  <p className="text-body">Revenue with Promotions</p>
                  <p className="text-body-bold">$1200</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-body">Discount Given</p>
                  <p className="text-danger">-$600</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-body">Net Revenue</p>
                  <p className="text-success">$46</p>
                </div>
                <div className="border-b border-gray-200"></div>
                <div className="flex justify-between items-center">
                  <p className="text-body-bold">ROI</p>
                  <p className="text-body-bold">+23%</p>
                </div>
              </div>
            }
          />
        </div>
      )}

      {!discountData && (
        <div className="flex justify-end items-end py-5 mt-7">
          <CustomPagination
            page={currentPage}
            initialPage={1}
            total={totalPage}
            size="md"
            onChange={setCurrentPage}
            // itemPerPage={viewItemPerPage}
            // onItemPerPageChange={(value) => setViewItemPerPage(value)}
          />
        </div>
      )}

      {/* <Table
        aria-label="Discount Table"
        selectionMode="single"
        color="success"
        isStriped
        shadow="none"
      >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.key}
              className="bg-gray-500 text-white font-bold uppercase"
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>

        <TableBody emptyContent={"No data to display"} items={rows}>
          {(item) => (
            <TableRow key={item.id}>
              <TableCell>{item.id}</TableCell>
              <TableCell>{item.service}</TableCell>
              <TableCell>{item.type}</TableCell>
              <TableCell>{item.value_type}</TableCell>
              <TableCell>{item.amount}</TableCell>
              <TableCell>{item.city}</TableCell>
              <TableCell>{item.state || "-"}</TableCell>
              <TableCell>{item.country}</TableCell>
              <TableCell>{item.PhoneNo}</TableCell>
              <TableCell>{item.createAt}</TableCell>
              <TableCell>
                {item.status ? (
                  <CustomChip label="Active" color="success" />
                ) : (
                  <CustomChip label="Inactive" color="danger" />
                )}
              </TableCell>
              <TableCell className="flex flex-initial">
                <CustomButton
                  type="button"
                  isIconOnly
                  className="bg-transparent"
                  startContent={
                    <RiDeleteBin6Line
                      size={18}
                      className="text-red-500 hover:text-red-700"
                    />
                  }
                />
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table> */}
    </>
  );
};

export default AllPromotion;
