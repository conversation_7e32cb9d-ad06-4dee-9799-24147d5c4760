import { Progress, ProgressProps } from "@heroui/react";

interface ProgressBarProps extends ProgressProps {
  lable?: string;
  value: number;
  showValueLabel?: boolean;
  size?: "sm" | "md" | "lg";
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
  isStriped?: boolean;
  minValue?: number;
  maxValue?: number;
  isDisabled?: boolean;
}

const CustomProgressBar = ({
  lable,
  value,
  showValueLabel,
  size = "sm",
  minValue,
  maxValue,
  isDisabled,
  isStriped,
  color,
}: ProgressBarProps) => {
  return (
    <div className="flex flex-col">
      <Progress
        label={lable}
        size={size}
        value={value}
        minValue={minValue}
        maxValue={maxValue}
        isDisabled={isDisabled}
        isStriped={isStriped}
        showValueLabel={showValueLabel}
        color={color}
      />
    </div>
  );
};

export default CustomProgressBar;
