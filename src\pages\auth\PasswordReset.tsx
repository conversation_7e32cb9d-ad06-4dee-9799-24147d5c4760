import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CardFooter } from "@heroui/react";
import CustomInput from "../../components/ui/CustomInput";
import logo from "../../assets/newlogoNew.png";
import { useNavigate } from "react-router-dom";
import CustomButton from "../../components/ui/CustomButton";

const PasswordReset = () => {
  const navigate = useNavigate();

  const goToConfirmCodepage = () => {
    navigate("/confirm-code");
  };

  return (
    <div className="min-h-screen grid grid-cols-1 laptop:grid-cols-2 desktop:grid-cols-3">
      {/* Right Side - Image */}
      <div className="hidden  desktop:col-span-2 laptop:flex justify-center items-center bg-blue-100">
        <img
          src="https://cdn.staging.gigmosaic.ca/service/vv.jpg-1753259572022"
          alt="Password Reset Illustration"
          className="object-cover w-full h-full"
        />
      </div>

      <div className="flex justify-center items-center bg-gray-50 p-4 dark:bg-darkModeBackgroundSecondary">
        <Card className="w-full max-w-lg shadow-lg rounded-2xl  desktop:py-6 tablet:p-6 p-1">
          <img src={logo} alt="" className="w-[300px] h-[80px] mx-auto" />
          <CardHeader className="text-center flex flex-col items-center">
            <h2 className="text-2xl font-bold">Change Your Password</h2>
            <p className="text-sm text-gray-500 mt-1 text-center">
              Enter your email address and we’ll send you a code to reset your
              password
            </p>
          </CardHeader>

          <CardBody className="space-y-4">
            <CustomInput
              type="email"
              label="Email"
              placeholder="Enter your email"
            />
          </CardBody>

          <CardFooter className="flex flex-col space-y-3">
            <CustomButton
              label=" Get Reset Code"
              className="w-full"
              color="primary"
              radius="md"
              size="md"
              onPress={goToConfirmCodepage}
            />

            {/* Left Side - Form */}
            <p className="text-xs text-gray-500 text-center">
              Don’t need to reset your password?{" "}
              <a href="/dashboard" className="text-blue-500 font-medium">
                Back to Dashboard
              </a>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default PasswordReset;
