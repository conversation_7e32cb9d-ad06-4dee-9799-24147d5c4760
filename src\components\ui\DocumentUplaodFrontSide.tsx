import { ReactNode, useState, ChangeEvent } from "react";
import { IoCloudUploadOutline } from "react-icons/io5";

interface DocumentUploadInputProps {
  title: string;
  subText?: string;
  icon?: ReactNode;
  value?: File | null; // controlled by react-hook-form
  error?: boolean;
  onChange?: (file: File | null) => void;
}

const DocumentUplaodFrontSide = ({
  title,
  subText,
  icon,
  value,
  error,
  onChange,
}: DocumentUploadInputProps) => {
  const [filePreview, setFilePreview] = useState<string | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      setFilePreview(URL.createObjectURL(file));
      onChange?.(file); // tell react-hook-form
    } else {
      setFilePreview(null);
      onChange?.(null);
    }
  };

  return (
    <div
      className={`p-2 border-2 rounded-lg dark:border-gray-900 dark:bg-darkModeBackground ${
        error ? "border-red-400 bg-red-50" : "border-gray-300"
      }`}
    >
      <label
        htmlFor="uploadFile-frontside"
        className="dark:bg-darkModeBackground text-slate-500 font-semibold text-base rounded max-w-md h-52 flex flex-col items-center justify-center cursor-pointer border-2 dark:border-gray-800 border-gray-300 border-dashed mx-auto"
      >
        {filePreview ? (
          <img
            src={filePreview}
            alt="Preview"
            className="w-full h-full object-cover rounded"
          />
        ) : (
          <>
            {icon || <IoCloudUploadOutline size={24} />}
            <span>{title}</span>
            <p className="text-xs font-medium text-slate-400 mt-2">
              {subText || "PNG, JPG are allowed and Max size 5MB"}
            </p>
          </>
        )}
        <input
          type="file"
          id="uploadFile-frontside"
          className="hidden"
          accept="image/png, image/jpeg, image/svg+xml, image/webp, image/gif"
          onChange={handleFileChange}
        />
      </label>
    </div>
  );
};

export default DocumentUplaodFrontSide;
