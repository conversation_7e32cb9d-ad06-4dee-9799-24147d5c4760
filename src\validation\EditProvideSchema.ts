import * as yup from "yup";
import {
  addressRule,
  birthdayRule,
  cityRule,
  countryRule,
  currencyRule,
  languageRule,
  nameRule,
  phoneNoRule,
  pincodeRule,
  provideBio,
  stateRule,
} from "./ValidationRules";

export const editProviderSchema = yup.object().shape({
  name: nameRule,
  mobile: phoneNoRule,
  dateOfBirth: birthdayRule,
  bio: provideBio,
  address: yup.object().shape({
    addressLine1: addressRule,
    country: countryRule,
    state: stateRule,
    city: cityRule,
    postalCode: pincodeRule,
  }),
  language: languageRule,
  currencyCode: currencyRule,
});
