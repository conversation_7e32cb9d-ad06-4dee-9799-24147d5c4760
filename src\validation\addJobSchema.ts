import * as yup from "yup";
import { requireDescription } from "./ValidationRules";

export const addJobSchema = yup.object().shape({
  bookingId: yup.string().required("Booking Id is required"),
  serviceId: yup.string().required("Service Id is required"),
  providerId: yup.string().required("Provider Id  is required"),
  description: requireDescription,
  //   images: yup.array().of(yup.mixed()).min(1, "At least one image is required"),
  images: yup
    .array()
    .of(
      yup
        .mixed<File | string>()
        .test("file-or-url", "Must be a file or a valid URL", (value) => {
          if (!value) return false;

          console.log("value: ", value);

          // Case 1: File
          if (value instanceof File) return true;

          // Case 2: URL string
          if (typeof value === "string") {
            try {
              new URL(value);
              return true;
            } catch {
              return false;
            }
          }

          return false;
        })
    )
    .min(1, "At least one image is required"),
});
