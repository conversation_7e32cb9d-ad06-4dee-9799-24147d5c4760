import CustomInput from "../../components/ui/CustomInput";
import {
  Autocomplete,
  AutocompleteItem,
  Card,
  DateInput,
  Divider,
} from "@heroui/react";
import CustomButton from "../../components/ui/CustomButton";
import { Controller, useFormContext } from "react-hook-form";
import { useEffect, useState } from "react";
import { CalendarDate } from "@internationalized/date";
import {
  canadaProvincesAndTerritories,
  countryData,
  individualDocumentType,
} from "../../data/sampleData";
import CustomDivider from "../../components/ui/CustomDivider";
import { IoCloudUploadOutline } from "react-icons/io5";
import DocumentUploadBackSide from "../../components/ui/DocumentUploadBackSide";
import DocumentUplaodFrontSide from "../../components/ui/DocumentUplaodFrontSide";
import { ProviderVerifySubmitProps } from "../../types";
import { convertToInternationalizedDateTimeToReadble } from "../../utils/convertTime";

const VerifyIndividual = () => {
  const [documnetLabel, setDocumentLabel] = useState<string>("");

  const {
    register,
    control,
    watch,
    formState: { errors },
  } = useFormContext<ProviderVerifySubmitProps>();

  const docType = watch("documents.documentType");

  useEffect(() => {
    if (docType === "Passport") {
      setDocumentLabel("Passport No");
    } else if (docType === "Driving License") {
      setDocumentLabel("Driving License No");
    } else if (docType === "National Identity Card") {
      setDocumentLabel("NIC No");
    } else {
      setDocumentLabel("Document No");
    }
  }, [docType]);

  console.log("Hook data: ", watch());

  return (
    <>
      <Card radius="none" shadow="none">
        <div className="mx-5 ">
          {/* GENERAL INFORMATION */}
          <div>
            <div className="grid md:grid-cols-2 gap-5 mt-2">
              <div className="grid col-span-2 md:grid-cols-3 gap-3 mt-2">
                <div>
                  <CustomInput
                    label="First Name"
                    isRequireField={true}
                    radius="sm"
                    variant="bordered"
                    size="md"
                    placeholder="Enter your full name"
                    isInvalid={!!errors?.firstName}
                    errorMessage={errors?.firstName?.message}
                    type="text"
                    {...register("firstName")}
                  />
                </div>

                <div>
                  <CustomInput
                    label="Middle Name"
                    radius="sm"
                    variant="bordered"
                    size="md"
                    placeholder="Enter your full name"
                    isInvalid={!!errors?.middleName}
                    errorMessage={errors?.middleName?.message}
                    type="text"
                    {...register("middleName")}
                  />
                </div>

                <div>
                  <CustomInput
                    label="Last Name"
                    radius="sm"
                    isRequireField={true}
                    variant="bordered"
                    size="md"
                    placeholder="Enter your full name"
                    isInvalid={!!errors?.lastName}
                    errorMessage={errors?.lastName?.message}
                    type="text"
                    {...register("lastName")}
                  />
                </div>
              </div>
              {/* <div>
                <CustomInput
                  type="text"
                  radius="sm"
                  variant="bordered"
                  size="md"
                  label="Mobile Number"
                  placeholder="Enter your phone number"
                  description="Format: +1XXXXXXXXXX (no spaces)"
                  {...register("mobile")}
                  isInvalid={!!errors?.mobile}
                  errorMessage={errors?.mobile?.message}
                />
              </div> */}

              <div>
                <Controller
                  name={"dateOfBirth"}
                  control={control}
                  render={({ field }) => (
                    <DateInput
                      label="Date of Birth"
                      variant="bordered"
                      labelPlacement="outside"
                      onChange={(val) => {
                        if (val) {
                          const date =
                            convertToInternationalizedDateTimeToReadble(
                              val as CalendarDate
                            );
                          field.onChange(date);
                        } else {
                          field.onChange("");
                        }
                      }}
                    />
                  )}
                />
              </div>
            </div>
          </div>

          {/* ADDRESS INFORMATION */}
          <div className="mt-8">
            <div className="flex flex-initial justify-between items-center">
              <p className="text-subtitle3">Address Information</p>
            </div>
            <Divider className="my-3" />
            <div className="mt-12 mb-6">
              <CustomInput
                label="Address"
                isRequireField={true}
                radius="sm"
                variant="bordered"
                size="md"
                placeholder="Enter your address"
                type="text"
                {...register("address.addressLine1")}
                isInvalid={!!errors?.address?.addressLine1}
                errorMessage={errors?.address?.addressLine1?.message}
              />
            </div>
            <div className="grid grid-cols-2 gap-5 mt-2">
              <Controller
                name="address.country"
                control={control}
                render={({ field, fieldState }) => (
                  <Autocomplete
                    {...field}
                    onSelectionChange={(key) => field.onChange(key)}
                    selectedKey={field.value}
                    radius="sm"
                    labelPlacement="outside"
                    size="md"
                    variant="bordered"
                    label="Country"
                    placeholder="Enter your country"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                  >
                    {countryData.map((s) => (
                      <AutocompleteItem key={s.key}>{s.label}</AutocompleteItem>
                    ))}
                  </Autocomplete>
                )}
              />

              <Controller
                name="address.state"
                control={control}
                render={({ field, fieldState }) => (
                  <Autocomplete
                    {...field}
                    onSelectionChange={(key) => field.onChange(key)}
                    selectedKey={field.value}
                    radius="sm"
                    labelPlacement="outside"
                    size="md"
                    variant="bordered"
                    label="State"
                    placeholder="Enter your state"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                  >
                    {canadaProvincesAndTerritories.map((s) => (
                      <AutocompleteItem key={s.key}>{s.label}</AutocompleteItem>
                    ))}
                  </Autocomplete>
                )}
              />

              <div>
                <CustomInput
                  radius="sm"
                  variant="bordered"
                  isRequireField={true}
                  size="md"
                  label="City"
                  placeholder="Enter your city"
                  type="tel"
                  {...register("address.city")}
                  isInvalid={!!errors?.address?.city}
                  errorMessage={errors?.address?.city?.message}
                />
              </div>

              <div>
                <CustomInput
                  radius="sm"
                  variant="bordered"
                  isRequireField={true}
                  size="md"
                  label="Postal Code"
                  placeholder="Enter postal code"
                  type="tel"
                  {...register("address.postalCode")}
                  isInvalid={!!errors?.address?.postalCode}
                  errorMessage={errors?.address?.postalCode?.message}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-5 "></div>
          </div>

          <div className="mt-8 mb-10">
            <div className="flex flex-initial justify-between items-center">
              <p className="text-subtitle3">Document Upload</p>
            </div>

            <div className="my-3">
              <CustomDivider />
            </div>

            <div>
              <div className="grid md:grid-cols-2 gap-5 mt-5">
                <Controller
                  name="documents.documentType"
                  control={control}
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      {...field}
                      onSelectionChange={(key) => field.onChange(key)}
                      selectedKey={field.value}
                      radius="sm"
                      labelPlacement="outside"
                      size="md"
                      variant="bordered"
                      label="Document Type"
                      placeholder="Select a document type"
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                    >
                      {individualDocumentType.map((s) => (
                        <AutocompleteItem key={s.key}>
                          {s.label}
                        </AutocompleteItem>
                      ))}
                    </Autocomplete>
                  )}
                />

                <CustomInput
                  label={documnetLabel}
                  radius="sm"
                  variant="bordered"
                  isRequireField={true}
                  size="md"
                  placeholder="Enter number"
                  type="text"
                  {...register("documents.documentNo")}
                  isInvalid={!!errors?.documents?.documentNo}
                  errorMessage={errors?.documents?.documentNo?.message}
                />

                <div>
                  <Controller
                    name={"documents.issueDate"}
                    control={control}
                    render={({ field }) => (
                      <DateInput
                        label="Issue Date"
                        variant="bordered"
                        labelPlacement="outside"
                        onChange={(val) => {
                          if (val) {
                            const date =
                              convertToInternationalizedDateTimeToReadble(
                                val as CalendarDate
                              );
                            field.onChange(date);
                          } else {
                            field.onChange("");
                          }
                        }}
                      />
                    )}
                  />
                </div>

                <div>
                  <Controller
                    name={"documents.expiryDate"}
                    control={control}
                    render={({ field }) => (
                      <DateInput
                        label="Expire Date"
                        variant="bordered"
                        labelPlacement="outside"
                        onChange={(val) => {
                          if (val) {
                            const date =
                              convertToInternationalizedDateTimeToReadble(
                                val as CalendarDate
                              );
                            field.onChange(date);
                          } else {
                            field.onChange("");
                          }
                        }}
                      />
                    )}
                  />
                </div>
              </div>

              <div className="grid xl:grid-cols-2 gap-5 my-5">
                <div>
                  <p className="text-subtitle1 my-3">Front Side</p>
                  <Controller
                    name="documents.front"
                    control={control}
                    render={({ field }) => (
                      <DocumentUplaodFrontSide
                        title="Upload Front Side"
                        icon={<IoCloudUploadOutline size={25} />}
                        value={field.value}
                        onChange={field.onChange}
                        error={!!errors?.documents?.front}
                      />
                    )}
                  />

                  <small className="text-error">
                    {errors?.documents?.front?.message}
                  </small>

                  <small className="flex flex-col text-gray-500">
                    Note: Make sure the entire front side is visible, clear, and
                    well-lit.
                  </small>
                </div>

                {docType !== "Passport" && (
                  <div>
                    <p className="text-subtitle1 my-3">Back Side</p>
                    <Controller
                      name="documents.back"
                      control={control}
                      render={({ field }) => (
                        <DocumentUploadBackSide
                          title="Uplaod Back Side"
                          icon={<IoCloudUploadOutline size={25} />}
                          value={field.value}
                          onChange={field.onChange}
                          error={!!errors?.documents?.back}
                        />
                      )}
                    />
                    <small className="text-error">
                      {errors?.documents?.back?.message}
                    </small>

                    <small className="flex flex-col text-gray-500">
                      Note: Ensure the back side is fully visible and all text
                      is readable.
                    </small>
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-5 mt-6"></div>
          </div>

          <div className="flex justify-end items-center mt-10 gap-5">
            <CustomButton
              type="button"
              size="sm"
              label="Close"
              color="danger"
              variant="light"
            />
            <CustomButton
              type="submit"
              size="sm"
              label="Submit"
              color="primary"
            />
          </div>
        </div>
      </Card>
    </>
  );
};

export default VerifyIndividual;
