// ServiceDownContext.tsx
import { createContext, useContext, useState, useEffect } from "react";
import { registerStoreSetter } from "./AppStore";

type ServiceDownContextType = {
  isDown: boolean;
  setDown: (val: boolean) => void;
};

const ServiceDownContext = createContext<ServiceDownContextType>({
  isDown: false,
  setDown: () => {},
});

export const ServiceDownProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [isDown, setDown] = useState(false);

  useEffect(() => {
    registerStoreSetter("serviceDown", setDown);
  }, []);

  return (
    <ServiceDownContext.Provider value={{ isDown, setDown }}>
      {children}
    </ServiceDownContext.Provider>
  );
};

export const useServiceDown = () => useContext(ServiceDownContext);
