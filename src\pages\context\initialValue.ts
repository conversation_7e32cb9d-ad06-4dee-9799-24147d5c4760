export interface AppStoreType {
  serviceData: any;
  setServiceData: (data: any) => void;

  userData: any;
  setUserData: (data: any) => void;

  isDown: boolean;
  setDown: (val: boolean) => void;

  bookingData: any[];
  setBookingData: (data: any[]) => void;
}

// Default values
export const defaultStore: AppStoreType = {
  serviceData: null,
  setServiceData: () => {},

  userData: null,
  setUserData: () => {},

  isDown: false,
  setDown: () => {},

  bookingData: [],
  setBookingData: () => {},
};
