import { useState, ReactNode } from "react";

interface StepperProps {
  title: string;
  content: ReactNode;
}

interface CommonStepperProps {
  steps: StepperProps[];
}

const CommonStepper = ({ steps }: CommonStepperProps) => {
  const [currentStep, setCurrentStep] = useState(0);

  const goNext = () => {
    if (currentStep < steps.length - 1) setCurrentStep(currentStep + 1);
  };

  const goPrev = () => {
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  };

  return (
    <div className="mx-auto p-6">
      {/* Top Step Indicator */}
      <div className="flex justify-between mb-8">
        {steps.map((step, index) => (
          <div key={index} className="flex-1">
            <div className="flex flex-col items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center mb-2
                ${
                  currentStep === index
                    ? "bg-blue-600 text-white font-bold"
                    : currentStep > index
                    ? "bg-green-500 text-white"
                    : "bg-gray-300 text-gray-600"
                }`}
              >
                {index + 1}
              </div>
              <div
                className={`text-sm font-medium text-center ${
                  currentStep === index
                    ? "text-blue-600"
                    : currentStep > index
                    ? "text-green-600"
                    : "text-gray-500"
                }`}
              >
                {step.title}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Current Step Content */}
      <div className="min-h-[180px] mb-6 p-4 border rounded">
        {steps[currentStep].content}
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={goPrev}
          disabled={currentStep === 0}
          className={`px-4 py-2 rounded border ${
            currentStep === 0
              ? "text-gray-400 border-gray-400 cursor-not-allowed"
              : "text-blue-600 border-blue-600 hover:bg-blue-100"
          }`}
        >
          Previous
        </button>

        <button
          onClick={goNext}
          disabled={currentStep === steps.length - 1}
          className={`px-4 py-2 rounded border ${
            currentStep === steps.length - 1
              ? "text-gray-400 border-gray-400 cursor-not-allowed"
              : "text-blue-600 border-blue-600 hover:bg-blue-100"
          }`}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default CommonStepper;
