import { IoSearchSharp } from "react-icons/io5";
import CustomInput from "../../components/ui/CustomInput";
import PageHeader from "../../components/ui/PageHeader";
import CustomButton from "../../components/ui/CustomButton";
import { Md<PERSON><PERSON><PERSON>, MdEditSquare } from "react-icons/md";
import { useMemo, useState } from "react";
import StatsCard from "../../components/ui/StatsCard";
import TabNav from "../../components/ui/TabNav";
import {
  BsClockFill,
  BsFillBagCheckFill,
  BsFillCheckCircleFill,
} from "react-icons/bs";
import { RiTimerFlashFill } from "react-icons/ri";
import CustomCardWithHeader from "../../components/ui/CustomCardWithHeader";
import {
  useFetchAllBookings,
  useFetchBookingByReferenceId,
} from "../../hooks/queries/useFetchData";
import { BookingStatus, IBookingProps } from "../../types";
import moment from "moment";
import CustomChip from "../../components/ui/CustomChip";
import CustomPagination from "../../components/ui/CustomPagination";
import CommonReply from "../../components/ui/CommonReply";
import { addressFormatHelper } from "../../utils/common";
import { Accordion, AccordionItem, addToast } from "@heroui/react";
import CustomStepper from "../../components/ui/CustomStepper";
import { FaBookmark } from "react-icons/fa";
import CancelledStepper from "../../components/ui/CancelledStepper";
import ConfirmToast from "../../components/ui/ConfirmToast";
import { useUpdateBookingStatus } from "../../hooks/mutations/useUpdateData";
import InputToast from "../../components/ui/InputToast";
import SmallLoadingSpinner from "../../components/ui/SmallLoadingSpinner";
import NotFoundData from "../../components/ui/NotFoundData";
import NoDataFound from "../NoDataFound";
import BookingReshedule from "./BookingReshedule";
import AddJob from "../jobManagment/AddJob";

const AllBooking = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchInput, setSearchInput] = useState<string>("");
  const [searchId, setSearchId] = useState<string>("");

  const { data, isFetching } = useFetchAllBookings(currentPage, 10);

  const {
    data: searchData,
    isError: isSearchError,
    isFetching: isSearching,
    error: searchError,
  } = useFetchBookingByReferenceId(searchId);

  const { mutate } = useUpdateBookingStatus();

  console.log("Error: ", searchError);

  let booking: IBookingProps[];

  if (searchData?.booking) {
    booking = [searchData.booking];
  } else {
    booking = data?.bookings || [];
  }

  const totalPage = useMemo(() => data?.pages || 1, [data]);

  console.log("Booking data: ", booking);
  console.log("data data: ", searchData);

  const tabs = [
    { id: "today", title: "Today" },
    { id: "all", title: "All Bookings" },
    { id: "pending", title: "Pending" },
    { id: "inprogress", title: "Inprogress" },
    { id: "complete", title: "Completed" },
    { id: "cancelled", title: "Cancelled" },
    { id: "analytics", title: "Analytics" },
  ];

  const statusColorMap: Record<
    string,
    "success" | "danger" | "warning" | "secondary" | "primary"
  > = {
    Completed: "success",
    Pending: "warning",
    Confirmed: "primary",
    Inprogress: "secondary",
    Cancelled: "danger",
  };

  const handleStatusUpdate = (
    bookingId: string,
    status: string,
    referenceId: string,
    title: string,
    appointmentDate?: string,
    appointmentStartTime?: string
  ) => {
    const now = moment();
    const appointmentMoment = moment(appointmentDate);

    if (appointmentMoment.diff(now, "hours") < 24) {
      console.log("Cannot cancel within 24 hours of appointment.");
      // Optionally show a toast to user
      addToast({
        description:
          "Bookings cannot be cancelled within 24 hours of the appointment.",
        radius: "md",
        color: "danger",
      });
      return;
    }

    ConfirmToast({
      title: `${title}`,
      message: `Are you sure you want to change the status of "${referenceId}" to "${status}"?. This action cannot be reverted to the previous status.${
        status === BookingStatus.CONFIRMED
          ? " After acceptance, the booking can only be rescheduled or cancelled."
          : ""
      }`,
      type: "warning",
      confirmText: "Yes",
      cancelText: "No",
    }).then(async (confirmed) => {
      if (confirmed) {
        if (status === BookingStatus.CANCELLED) {
          const reason = await InputToast({
            title: "Please provide a cancelation reason",
            placeholder: "Please provide a reason",
            subText:
              "Please provide a reason for cancelling this booking. The customer will be notified so they understand the cancellation reason.",
            confirmText: "Confirm",
            cancelText: "Cancel",
            showCancelBtn: true,
            minValue: 5,
            maxValue: 100,
          });

          if (reason) {
            mutate({
              id: bookingId,
              newStatus: status,
              note: reason,
            });
          } else {
            addToast({
              title: "Error",
              description: "Error cancelation reason. Please try again.",
              radius: "md",
              color: "danger",
            });
            return;
          }
        }

        mutate({ id: bookingId, newStatus: status });
      }
    });
  };

  const handleSearch = () => setSearchId(searchInput);
  const clearSearch = () => setSearchId("");

  return (
    <div>
      <PageHeader
        title="Bookings"
        description="Manage your service bookings and appointments"
        components={
          <div className="flex gap-5">
            <div className="flex flex-initial justify-end items-center">
              <CustomInput
                isClearable
                placeholder="Search by booking ID"
                type="text"
                size="sm"
                className="md:w-[350px]"
                onValueChange={(e) => setSearchInput(e)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSearch();
                  }
                }}
                onClear={() => clearSearch()}
              />

              <CustomButton
                label="Search"
                className="ml-2"
                color="secondary"
                onPress={handleSearch}
                isLoading={isSearching}
              />

              <div className="ml-5">
                <CustomButton
                  label="Add  booking"
                  //   onPress={handleNavigate}
                  startContent={<MdEditSquare />}
                  color={"primary"}
                  variant="solid"
                />
              </div>
            </div>
          </div>
        }
      />

      <div className="grid tablet:grid-cols-2 laptop:grid-cols-5 gap-5">
        <StatsCard
          title="All Bookings"
          isHoverEffect={true}
          value={data?.total || "-"}
          isLoading={isFetching}
          icon={<FaBookmark size={30} className=" text-secondary" />}
        />
        <StatsCard
          title="Pending"
          isHoverEffect={true}
          value={28}
          isLoading={isFetching}
          icon={<BsClockFill size={30} className=" text-warning" />}
        />
        <StatsCard
          title="Inprogress"
          isHoverEffect={true}
          value={"$1200"}
          isLoading={isFetching}
          icon={<RiTimerFlashFill size={34} className=" text-secondary" />}
        />
        <StatsCard
          title="Completed"
          isHoverEffect={true}
          value={"28%"}
          isLoading={isFetching}
          icon={<BsFillCheckCircleFill size={30} className=" text-success" />}
        />
        <StatsCard
          title="Cancelled"
          isHoverEffect={true}
          value={"28%"}
          isLoading={isFetching}
          icon={<MdCancel size={34} className=" text-danger" />}
        />
      </div>

      <TabNav activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />
      {/* </div> */}
      {/* <div className="col-span-2 bg-green-200">Hi</div> */}

      <div className="mt-5">
        {isFetching || isSearching ? (
          <SmallLoadingSpinner size="md" variant="wave" />
        ) : isSearchError || booking.length === 0 ? (
          <NoDataFound isIcon />
        ) : (
          <>
            {booking?.map((b: IBookingProps, index: number) => (
              <CustomCardWithHeader
                key={index}
                title={b?.serviceName}
                isChip={true}
                chipText={b.bookingStatus}
                chipVariant="bordered"
                chipColor={statusColorMap[b.bookingStatus] || "secondary"}
                className=" hover:shadow-md mt-5"
                mainContent={
                  <div className="-mt-4">
                    <div className="grid grid-cols-2 tablet:grid-cols-4 laptop:grid-cols-7 text-body gap-4">
                      <div className="flex flex-col ">
                        <p className="text-body">Booking ID</p>
                        <p className="text-body-bold font-primary">
                          {b?.referenceCode || "-"}
                        </p>
                      </div>

                      <div className="flex flex-col ">
                        <p className="text-body">Booking Date</p>
                        <p className="text-body-bold font-primary">
                          {moment(b.appointmentDate).format("DD MMM YYYY") ||
                            "-"}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Appointment Time</p>
                        <p className="text-body-bold font-primary">
                          {b?.appointmentTimeFrom || b?.appointmentTimeTo
                            ? `${b?.appointmentTimeFrom || "-"} - ${
                                b?.appointmentTimeTo || "-"
                              }`
                            : "-"}
                        </p>
                      </div>

                      <div className="flex flex-col ">
                        <p className="text-body">Paid Status</p>
                        <CustomChip
                          label={b.isPaid ? "Paid" : "Unpaid"}
                          color={b.isPaid ? "success" : "danger"}
                        />
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Payment Method</p>
                        <p className="text-body-bold">
                          {b?.paymentMethod || "-"}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Total</p>
                        <p className="text-body-bold">${b?.total || "-"}</p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Booking Created On</p>
                        <p className="text-body-bold">
                          {moment(b.createdAt).format("DD MMM YYYY, hh:mm A") ||
                            "-"}
                        </p>
                      </div>

                      {/* Customer */}
                      <div className="flex flex-col">
                        <p className="text-body">Customer</p>
                        <p className="text-body-bold">{`${b?.personalInfo?.firstName} ${b?.personalInfo?.lastName}`}</p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Address</p>
                        <p className="text-body-bold">
                          {addressFormatHelper(b.personalInfo?.address)}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Email</p>
                        <p className="text-body-bold">
                          {b?.personalInfo?.email || "-"}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Reshedule</p>
                        <p className="text-body-bold">
                          {/* <CustomChip
                        label={resheduleStatus.length === 0 ? "No" : "Yes"}
                        color={
                          resheduleStatus.length === 0 ? "danger" : "success"
                        }
                      /> */}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Last Reshedule</p>
                        <p className="text-body-bold">
                          {moment(b.createdAt).format("DD MMM YYYY, hh:mm A") ||
                            "-"}
                        </p>
                      </div>
                    </div>

                    {/* BOOKING NOTE */}
                    {b?.personalInfo?.bookingNotes && (
                      <CommonReply
                        title="Special Instructions"
                        description={b?.personalInfo?.bookingNotes}
                      />
                    )}

                    {/* ADDTIONAL DETAILS */}
                    {b.additionalServices?.length > 0 && (
                      <div className="mt-3  bg-white rounded-xl">
                        <Accordion
                          isCompact
                          variant="bordered"
                          fullWidth
                          className="mt-2"
                        >
                          <AccordionItem
                            classNames={{
                              title: "text-body-bold",
                            }}
                            key="1"
                            aria-label={b?.bookingId}
                            title={`${"Additonal Service"} (${
                              b?.additionalServices?.length
                            })`}
                          >
                            <div className="grid grid-cols-2 tablet:grid-cols-4 laptop:grid-cols-7 text-body gap-4">
                              <div className="flex flex-col ">
                                <p className="text-body mb-1">Service Name</p>
                                {b?.additionalServices?.map((a, index) => (
                                  <p
                                    key={index}
                                    className="text-body-bold font-primary mb-1"
                                  >
                                    {a?.serviceItem || "-"}
                                  </p>
                                ))}
                              </div>

                              <div className="flex flex-col ">
                                <p className="text-body mb-1">Price</p>
                                {b?.additionalServices?.map((a, index) => (
                                  <p
                                    key={index}
                                    className="text-body-bold font-primary mb-1"
                                  >
                                    ${a?.price || "-"}
                                  </p>
                                ))}
                              </div>
                            </div>
                          </AccordionItem>
                        </Accordion>
                      </div>
                    )}

                    {/* PAYMENT DETAILS */}
                    <div className="mt-2  bg-white rounded-xl">
                      <Accordion
                        isCompact
                        variant="bordered"
                        fullWidth
                        className="mt-2"
                      >
                        <AccordionItem
                          classNames={{
                            title: "text-body-bold",
                          }}
                          key="1"
                          aria-label={b?.referenceCode}
                          title="Payment Details"
                        >
                          <div className="grid grid-cols-2 tablet:grid-cols-4 laptop:grid-cols-7 text-body gap-4">
                            <div className="col-span-3">
                              <div className="flex justify-between items-center border-b mb-2">
                                <p className="text-body-bold mb-1">
                                  Service Price
                                </p>
                                <p className="text-body-bold ">
                                  ${b?.serviceDetails?.price}
                                </p>
                              </div>

                              <div className="mb-2">
                                <p className="text-caption mb-2 ">
                                  Additional Services
                                </p>
                                <ul className="list-none space-y-1 mt-1">
                                  {b?.additionalServices?.map((s, index) => (
                                    <li
                                      key={index}
                                      className="flex justify-between text-body-bold"
                                    >
                                      <p>
                                        {index + 1}. {s?.serviceItem}
                                      </p>
                                      <p className=" ml-2 text-body1">
                                        ${s?.price?.toFixed(2) || "0.00"}
                                      </p>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              <div className="flex justify-between items-center border-t mt-2">
                                <p className="text-body-bold mb-1 mt-1">
                                  Subtotal
                                </p>
                                <p className="text-body-bold ">
                                  ${b?.subtotal}
                                </p>
                              </div>
                              <div className="flex justify-between items-center mt-1">
                                <p className="text-success mb-1">Discount</p>
                                <p className="text-success ">-$738</p>
                              </div>
                              <div className="flex justify-between items-center border-b mt-1">
                                <p className="text-danger mb-1">Tax</p>
                                <p className="text-danger ">+$738</p>
                              </div>
                              <div className="flex justify-between items-center mt-1 border-b last:border-b-0">
                                <p className="text-body-bold mb-1">Price</p>
                                <p className="text-title ">${b?.total}</p>
                              </div>
                            </div>
                          </div>
                        </AccordionItem>
                      </Accordion>
                    </div>

                    {/* HISTORY */}
                    <div className="mt-2  bg-white rounded-xl">
                      <Accordion
                        isCompact
                        variant="bordered"
                        fullWidth
                        className="mt-2"
                      >
                        <AccordionItem
                          classNames={{
                            title: "text-body-bold",
                            content: "flex justify-center items-center",
                          }}
                          key="1"
                          aria-label={b?.referenceCode}
                          title="Booking History"
                        >
                          <div className="my-5 w-full">
                            {b?.bookingStatus === "Cancelled" ? (
                              <CancelledStepper auditLogs={b?.auditLogs} />
                            ) : (
                              <CustomStepper
                                status={b?.bookingStatus}
                                auditLogs={b?.auditLogs}
                              />
                            )}
                          </div>
                        </AccordionItem>
                      </Accordion>
                    </div>

                    {/* CANCEL RESON */}
                    {b?.bookingStatus === BookingStatus.CANCELLED && (
                      <div className="mt-2  bg-white rounded-xl">
                        <Accordion
                          isCompact
                          variant="bordered"
                          fullWidth
                          className="mt-2"
                        >
                          <AccordionItem
                            classNames={{
                              title: "text-body-bold",
                              content: "flex justify-center items-center",
                            }}
                            key="1"
                            aria-label={b?.referenceCode}
                            title="Cancelation Reason"
                          >
                            <div className=" w-full">
                              <p className="block text-body mb-1">
                                Please provide a reason for cancelling this
                                booking. The customer will be notified so they
                                understand the cancellation reason
                              </p>
                            </div>
                          </AccordionItem>
                        </Accordion>
                      </div>
                    )}

                    {/* RESHEDULE */}
                    {b?.bookingStatus !== BookingStatus.CANCELLED &&
                      b?.bookingStatus !== BookingStatus.COMPLETED && (
                        <div className="mt-2 bg-white rounded-xl">
                          <Accordion
                            isCompact
                            variant="bordered"
                            fullWidth
                            className="mt-2"
                          >
                            <AccordionItem
                              classNames={{
                                title: "text-body-bold",
                                content: "flex justify-center items-center",
                              }}
                              key="1"
                              aria-label={b?.referenceCode}
                              title="Reschedule Appointment"
                            >
                              <div className="w-full">
                                <BookingReshedule {...b} />
                              </div>
                            </AccordionItem>
                          </Accordion>
                        </div>
                      )}
                  </div>
                }
                footerContent={
                  <div className="w-full">
                    <div className="grid grid-cols-7 gap-4">
                      {b?.bookingStatus === "InProgress" && (
                        <CustomButton
                          label="Mark Complete"
                          color="success"
                          variant="flat"
                          className="border border-green-500"
                          onPress={() =>
                            handleStatusUpdate(
                              b?.bookingId,
                              BookingStatus.COMPLETED,
                              b?.referenceCode,
                              " Complete Booking"
                            )
                          }
                        />
                      )}

                      {b?.bookingStatus === BookingStatus.PENDING && (
                        <CustomButton
                          label="Accept Booking"
                          color="primary"
                          variant="bordered"
                          className="border border-primary/50 bg-primary/20"
                          onPress={() =>
                            handleStatusUpdate(
                              b?.bookingId,
                              BookingStatus.CONFIRMED,
                              b?.referenceCode,
                              " Accept Booking"
                            )
                          }
                        />
                      )}

                      {b?.bookingStatus !== BookingStatus.PENDING &&
                        b?.bookingStatus !== BookingStatus.COMPLETED &&
                        b?.bookingStatus !== BookingStatus.CANCELLED && (
                          <CustomButton
                            label="Cancel Booking"
                            color="default"
                            variant="bordered"
                            className="text-xs border text-red-500  hover:bg-red-500 hover:border-red-500 hover:text-black transition-colors duration-300"
                            onPress={() =>
                              handleStatusUpdate(
                                b?.bookingId,
                                BookingStatus.CANCELLED,
                                b?.referenceCode,
                                "Cancel Booking"
                              )
                            }
                          />
                        )}

                      {b?.bookingStatus === BookingStatus.PENDING && (
                        <CustomButton
                          label="Decline"
                          color="default"
                          variant="bordered"
                          className="text-xs border text-red-500 border-red-300 hover:bg-red-500 hover:border-red-500 hover:text-black transition-colors duration-300"
                          onPress={() =>
                            handleStatusUpdate(
                              b?.bookingId,
                              BookingStatus.CANCELLED,
                              b?.referenceCode,
                              "Reject Booking"
                            )
                          }
                        />
                      )}

                      {b?.bookingStatus === BookingStatus.COMPLETED && (
                        // <CustomButton
                        //   label="Add to Profile"
                        //   color="primary"
                        //   variant="solid"
                        //   onPress={() => {}}
                        // />
                        <AddJob data={b} />
                      )}
                    </div>
                    {/* <p className="text-caption  mt-2">
                      Note: Bookings cannot be cancelled within 24 hours of the
                      appointment.
                    </p> */}
                  </div>
                }
              />
            ))}
          </>
        )}
      </div>

      {/* {!isSearchError ||
        (data?.bookings?.length === 0 && ( */}
      <div className="flex justify-end items-end py-5 mt-7">
        <CustomPagination
          page={currentPage}
          initialPage={1}
          total={totalPage}
          size="md"
          onChange={setCurrentPage}
          // itemPerPage={viewItemPerPage}
          // onItemPerPageChange={(value) => setViewItemPerPage(value)}
        />
      </div>
      {/* ))} */}
    </div>
  );
};

export default AllBooking;
