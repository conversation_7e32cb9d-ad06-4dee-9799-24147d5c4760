import { useAppStore } from "./context/AppStore";

const Dashboard = () => {
  const {
    serviceData,
    setServiceData,
    userData,
    setUserData,
    isDown,
    setDown,
    bookingData,
    setBookingData,
  } = useAppStore();

  // Log all store data
  console.log({ serviceData, userData, isDown, bookingData });

  // Example: update server down
  const handleServerDown = () => setDown(true);

  // Example: update user
  const loginUser = () =>
    setUserData({ id: "U_123", name: "<PERSON>", role: "provider" });

  return (
    <div>
      <button onClick={handleServerDown}>Set Service Down</button>
      <button onClick={loginUser}>Login User</button>
    </div>
  );
};
export default Dashboard;
