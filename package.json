{"name": "gigmosic-provider-dashboard-fe", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit --skipLib<PERSON><PERSON><PERSON> || true && vite build", "lint": "eslint .", "preview": "vite preview", "version": "standard-version", "test": "jest"}, "dependencies": {"@aws-amplify/auth": "^6.15.0", "@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/lib-storage": "^3.750.0", "@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/list": "^6.1.18", "@fullcalendar/multimonth": "^6.1.18", "@fullcalendar/react": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@geoapify/geocoder-autocomplete": "^2.1.0", "@heroui/react": "^2.7.2", "@heroui/use-theme": "^2.1.10", "@hookform/resolvers": "^5.2.1", "@internationalized/date": "^3.7.0", "@pubuduth-aplicy/chat-ui": "^2.1.78", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.66.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/react-icons": "^3.0.0", "autoprefixer": "^10.4.20", "aws-amplify": "^6.15.5", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "dotenv": "^16.4.7", "framer-motion": "^12.4.7", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "jest": "^29.7.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "moment": "^2.30.1", "oidc-client-ts": "^3.2.0", "pino": "^9.9.0", "postcss": "^8.5.3", "postcss-nesting": "^13.0.2", "primeicons": "^7.0.0", "primereact": "^10.9.2", "prop-types": "^15.8.1", "qrcode.react": "^4.2.0", "quill": "^2.0.3", "react": "^19.0.0", "react-day-picker": "^9.9.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-oidc-context": "^3.3.0", "react-router-dom": "6", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "sweetalert2": "^11.17.2", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.0", "tippy.js": "^6.3.7", "user-event": "^4.0.0", "vitest": "^3.0.9", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@hookform/devtools": "^4.4.0", "@testing-library/dom": "^10.4.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.16", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "standard-version": "^9.5.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}