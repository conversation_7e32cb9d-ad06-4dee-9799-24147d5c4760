import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, Divider } from "@heroui/react";
import { ReactNode } from "react";
import CustomChip from "./CustomChip";

type varint =
  | "solid"
  | "bordered"
  | "light"
  | "flat"
  | "faded"
  | "shadow"
  | "dot";

type color =
  | "default"
  | "primary"
  | "secondary"
  | "success"
  | "danger"
  | "warning";
interface ReusableCardProps {
  title: string;
  subtitle?: string;
  mainContent: ReactNode;
  footerContent?: ReactNode;
  // isFooterDevider?: boolean;
  isHeaderDevider?: boolean;
  isHoverEffect?: boolean;
  isChip?: boolean;
  isSecondChip?: boolean;
  secondChipColor?: color;
  secondChipText?: string;
  secondChipVariant?: varint;
  secondChipIcon?: ReactNode;
  chipColor?: color;
  chipText?: string;
  chipVariant?: varint;
  chipIcon?: ReactNode;
  className?: string;
  isThirdChip?: boolean;
  thirdChipColor?: color;
  thirdChipText?: string;
  thirdChipVariant?: varint;
  thirdChipIcon?: ReactNode;
}

export default function CustomCardWithHeader({
  title,
  subtitle,
  mainContent,
  footerContent,
  // isFooterDevider,
  isHoverEffect,
  isHeaderDevider,
  isChip,
  chipColor,
  chipText = "",
  chipVariant = "solid",
  chipIcon,
  isSecondChip,
  secondChipColor,
  secondChipText = "",
  secondChipIcon,
  secondChipVariant = "solid",
  className = "",
  isThirdChip,
  thirdChipColor,
  thirdChipText = "",
  thirdChipVariant = "solid",
  thirdChipIcon,
}: ReusableCardProps) {
  return (
    <Card
      radius="sm"
      className={`border border-secondary/30  p-2 shadow-md ${
        isHoverEffect ? "hover-primary-gradient" : ""
      }    ${className}`}
      // className={`border border-secondary/30  p-2 shadow-md    ${className}`}
    >
      <CardHeader className="flex flex-col items-start">
        <div className="flex items-center justify-start gap-3">
          <p className={`text-title ${!subtitle ? "mb-2" : ""}  `}>{title}</p>
          {isChip && (
            <CustomChip
              label={chipText}
              color={chipColor}
              startContent={chipIcon}
              variant={chipVariant}
              className="-mt-2"
            />
          )}

          {isSecondChip && (
            <CustomChip
              label={secondChipText}
              color={secondChipColor}
              startContent={secondChipIcon}
              variant={secondChipVariant}
              className="-mt-2"
            />
          )}

          {isThirdChip && (
            <CustomChip
              label={thirdChipText}
              color={thirdChipColor}
              startContent={thirdChipIcon}
              variant={thirdChipVariant}
              className="-mt-2"
            />
          )}
        </div>

        {subtitle && (
          <p className="text-caption text-gray-500 mb-2">{subtitle}</p>
        )}

        {isHeaderDevider && <Divider />}
      </CardHeader>
      <CardBody>{mainContent}</CardBody>
      {footerContent && (
        <CardFooter className=" ">
          {/* {isFooterDevider && <Divider />} */}
          {footerContent}
        </CardFooter>
      )}
    </Card>
  );
}
