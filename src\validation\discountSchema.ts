import * as yup from "yup";

export const discountSchema = yup.object().shape({
  isPackage: yup
    .boolean()
    .required("Package Status is required. this is a code error"),
  serviceId: yup.string().required("Service is required"),
  packageId: yup.string().when("isPackage", {
    is: true,
    then: (schema) => schema.required("Package is required"),
    otherwise: (schema) => schema.notRequired(),
  }),

  discount: yup.object().shape({
    isPackage: yup
      .boolean()
      .required("Package Status is required. this is a code error"),
    discountType: yup
      .string()
      .nullable()
      .test("discount type", "Invalid discount type", function (value) {
        const isPackage = this.parent?.isPackage;

        console.log("PROMOTION  ispackage: ", isPackage);

        if (isPackage) return true;

        if (!value)
          return this.createError({ message: "Discount type is required" });
        if (value !== "promo-code" && value !== "general-discount") {
          return this.createError({ message: "Invalid discount type" });
        }
        return true;
      }),

    valueType: yup
      .string()
      .nullable()
      .test(
        "discount value type",
        "Invalid discount value type",
        function (value) {
          const isDiscount = this.parent?.isDiscount;
          if (!isDiscount) return true;
          if (!value)
            return this.createError({ message: "Discount value is required" });
          if (value !== "percentage" && value !== "amount") {
            return this.createError({ message: "Invalid value type" });
          }
          return true;
        }
      ),

    durationType: yup
      .string()
      .nullable()
      .test(
        "discount duration type",
        "Invalid discount duration type",
        function (value) {
          //   const isDiscount = this.parent?.isDiscount;
          //   if (!isDiscount) return true;
          if (!value)
            return this.createError({
              message: "Discount duration is required",
            });
          if (value !== "life-time" && value !== "time-base") {
            return this.createError({ message: "Invalid duration type" });
          }
          return true;
        }
      ),

    amount: yup
      .number()
      .typeError("Amount must be a number")
      .nullable()
      .test("amount", "Invalid Amount", function (value) {
        // const isDiscount = this.parent.isDiscount;
        // if (!isDiscount) return true;
        if (value == null)
          return this.createError({ message: "Amount is required" });
        if (value <= 0)
          return this.createError({ message: "Amount must be greater than 0" });
        if (value > 1000000)
          return this.createError({ message: "Amount must be less than $1M" });
        return true;
      }),

    promoCode: yup
      .string()
      .nullable()
      .test("promoCode", "Invalid Code", function (value) {
        const isDiscount = this.parent.isDiscount;
        const isPromoCode = this.parent.discountType;
        if (!isDiscount) return true;
        if (isPromoCode !== "promo-code") return true;
        if (!value) return this.createError({ message: "Code is required" });
        if (value.length < 3)
          return this.createError({
            message: "Code must be at least 3 characters",
          });
        if (value.length > 15)
          return this.createError({
            message: "Code must be at most 15 characters",
          });
        return true;
      }),

    maxCount: yup
      .number()
      .nullable()
      .notRequired()
      .typeError("Max Count must be a number")
      .test("Max Count", "Invalid count", function (value) {
        // const isDiscount = this.parent.isDiscount;
        // if (!isDiscount) return true;
        if (value == null) return true;
        if (value > 100000)
          return this.createError({
            message: "Count must be less than 100000",
          });
        return true;
      }),

    duration: yup.object().shape({
      start: yup
        .string()
        .nullable()
        .test("Duration start", "Start date is required", function (value) {
          //   const isDiscount =
          //     this.parent?.isDiscount ?? this.from?.[0]?.value?.isDiscount;
          //   if (!isDiscount) return true;
          if (!value || !value.trim()) {
            return this.createError({ message: "Start date is required" });
          }
          return true;
        }),
      end: yup.string().nullable(),
    }),
  }),
});
