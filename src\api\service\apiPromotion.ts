import { IPageNumberAndLimt } from "../../types";
import { apiClient } from "../axios/apiClient";
import { Path } from "../axios/endpoint";

export const getAllPromotion = async ({
  page = 1,
  limit = 10,
}: IPageNumberAndLimt) => {
  const quary = `?page=${page}&limit=${limit}`;
  try {
    const res = await apiClient.get(`${Path.providerDiscount}${quary}`);
    return res.data;
  } catch (error: unknown) {
    console.error("Error get all promotion: ", error);
    throw error;
  }
};

// UPDATE DISCOUNT
export const updateDiscount = async (id: string, data: any) => {
  try {
    console.error("Payload: ", data);
    const res = await apiClient.put(
      Path.discountUpdate.replace(":id", id),
      data
    );
    return res.data;
  } catch (error: unknown) {
    console.error("Error update discount: ", error);
    throw error;
  }
};

// CREATE DISCOUNT
export const createDiscount = async (data: any) => {
  try {
    console.error("Payload: ", data);
    const res = await apiClient.post(Path.createDiscount, data);
    return res.data;
  } catch (error: unknown) {
    console.error("Error create discount: ", error);
    throw error;
  }
};

// UPDATE DISCOUNT BY ID
export const getDiscountById = async (id: string) => {
  try {
    const res = await apiClient.put(Path.discountUpdate.replace(":id", id));
    return res.data;
  } catch (error: unknown) {
    console.error("Error get by id discount: ", error);
    throw error;
  }
};
