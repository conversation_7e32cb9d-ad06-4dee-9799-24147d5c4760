// import DataTable from "../../components/ui/DataTable";
// import { IoSearchSharp } from "react-icons/io5";
// import CustomInput from "../../components/ui/CustomInput";
// import AddStaffModa from "./AddStaffModa";
// import { Alert } from "@heroui/react";
// import CustomButton from "../../components/ui/CustomButton";
// import { useAuth } from "react-oidc-context";
// import { useNavigate } from "react-router-dom";
// import { useEffect, useState } from "react";
// import ROLE from "../../Role";

// const AllStaff = () => {
//   const auth = useAuth();
//   const navigate = useNavigate();

//   const [isVerifyAccount, setIsVerifyAccount] = useState<boolean>(false);

//   const title = "Account Verification Required";
//   const description =
//     "You need to verify your provider account to continue. Please complete the verification process to access your dashboard and services.";

//   useEffect(() => {
//     if (auth?.isAuthenticated) {
//       const roles = auth?.user?.profile?.["cognito:groups"] as string[];
//       if (roles) {
//         const provider = roles?.includes(ROLE.PROVIDER);
//         const customer = roles?.includes(ROLE.CUSTOMER);

//         if (provider && customer) {
//           setIsVerifyAccount(true);
//         } else {
//           setIsVerifyAccount(false);
//         }
//         // console.log("CUS: ", customer);
//         // console.log("PRO: ", provider);
//       }
//     } else {
//       setIsVerifyAccount(false);
//     }
//   }, [auth?.isAuthenticated, auth?.user?.profile]);

//   // const handleSearchData = async (value:string) => {
//   //   console.log("Search Data: ",value)
//   // }

//   return (
//     <>
//       {/*VERIFY ALERT  */}
//       {/* {!isVerifyAccount && (
//         <div className="flex items-center justify-center w-full mb-3 -mt-1">
//           <Alert
//             variant="faded"
//             color="warning"
//             description={description}
//             title={title}
//             endContent={
//               <CustomButton
//                 onPress={() => navigate("/user/profile/security")}
//                 color="warning"
//                 size="sm"
//                 variant="flat"
//                 label="Verify Account"
//               />
//             }
//           />
//         </div>
//       )} */}
//       <div className="grid sm:grid-cols-2 gap-2 md:gap-5">
//         <div className="flex justify-start items-center gap-2 md:gap-4 mb-8 ">
//           <CustomInput
//             placeholder="Search..."
//             type="text"
//             size="sm"
//             endContent={
//               <IoSearchSharp
//                 size={20}
//                 className="hover:text-bg-primary cursor-pointer"
//               />
//             }
//             className="sm:max-w-[300px]"
//             // onValueChange={(e.value: string) => handleSearchData()}
//           />
//         </div>

//         <div className="flex justify-end items-center gap-4 mb-8 ">
//           {isVerifyAccount && <AddStaffModa />}

//         </div>
//       </div>
//       <DataTable />
//     </>
//   );
// };

// export default AllStaff;

import { useMemo, useState } from "react";
import PageHeader from "../../components/ui/PageHeader";
import CustomInput from "../../components/ui/CustomInput";
import CustomButton from "../../components/ui/CustomButton";
import AddStaffModa from "./AddStaffModa";
import StatsCard from "../../components/ui/StatsCard";
import TabNav from "../../components/ui/TabNav";
import { FaBookmark } from "react-icons/fa";
import { BsClockFill, BsFillCheckCircleFill } from "react-icons/bs";
import { RiTimerFlashFill } from "react-icons/ri";
import { MdCancel } from "react-icons/md";
import CustomCardWithHeader from "../../components/ui/CustomCardWithHeader";
import { useFetchStaff } from "../../hooks/queries/useFetchData";
import { addressFormatHelper } from "../../utils/common";
import { iStaffGetProps } from "../../types";
import moment from "moment";
import CustomProgressBar from "../../components/ui/CustomProgressBar";
import CustomPagination from "../../components/ui/CustomPagination";
import StaffLeavePage from "./StaffLeavePage";

const AllStaff = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [currentPage, setCurrentPage] = useState<number>(1);

  const { data } = useFetchStaff({ page: currentPage, limit: 10 });

  const staff: iStaffGetProps[] = data?.staff || [];

  console.log(data);
  const totalPage = useMemo(() => data?.pages || 1, [data]);

  const tabs = [
    { id: "all", title: "All Staff" },
    { id: "active", title: "Active" },
    { id: "inactive", title: "Inactive" },
    // { id: "leave", title: "Today Leave" },
    { id: "leave_calendar", title: "Leave Calendar" },

    { id: "analytics", title: "Analytics" },
  ];

  const handleProgressBarColor = (maxValue: number, usage: number) => {
    if (!maxValue && !usage) return "primary";
    const value = Number(((Number(usage) / Number(maxValue)) * 100).toFixed(2));

    console.log("Value: ", value);

    if (value <= 50) return "danger"; // below 50%
    if (value >= 50 && value < 60) return "warning"; // 50% - 69.99%
    if (value >= 70 && value < 80) return "secondary"; // 50% - 69.99%
    if (value >= 80) return "success"; // 70% - 100%

    return "primary"; // fallback
  };

  return (
    <div>
      <PageHeader
        title="Staff Management"
        description="Manage your staff and their information."
        components={
          <div className="flex gap-5">
            <div className="flex flex-initial justify-end items-center">
              <CustomInput
                isClearable
                placeholder="Search by booking ID"
                type="text"
                size="sm"
                className="md:w-[350px]"
                // onValueChange={(e) => setSearchInput(e)}
                // onKeyDown={(e) => {
                //   if (e.key === "Enter") {
                //     handleSearch();
                //   }
                // }}
                // onClear={() => clearSearch()}
              />

              <CustomButton
                label="Search"
                className="ml-2"
                color="secondary"
                // onPress={handleSearch}
                //  isLoading={isSearching}
              />

              <div className="ml-5">
                <AddStaffModa />
              </div>
            </div>
          </div>
        }
      />

      <div className="grid tablet:grid-cols-2 laptop:grid-cols-5 gap-5">
        <StatsCard
          title="All Staff"
          isHoverEffect={true}
          // value={data?.total || "-"}
          // isLoading={isFetching}
          icon={<FaBookmark size={30} className=" text-secondary" />}
        />
        <StatsCard
          title="Pending"
          isHoverEffect={true}
          value={28}
          // isLoading={isFetching}
          icon={<BsClockFill size={30} className=" text-warning" />}
        />
        <StatsCard
          title="Inprogress"
          isHoverEffect={true}
          value={"$1200"}
          // isLoading={isFetching}
          icon={<RiTimerFlashFill size={34} className=" text-secondary" />}
        />
        <StatsCard
          title="Completed"
          isHoverEffect={true}
          value={"28%"}
          // isLoading={isFetching}
          icon={<BsFillCheckCircleFill size={30} className=" text-success" />}
        />
        <StatsCard
          title="Cancelled"
          isHoverEffect={true}
          value={"28%"}
          // isLoading={isFetching}
          icon={<MdCancel size={34} className=" text-danger" />}
        />
      </div>

      <TabNav activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />

      <div className="grid mt-5 gap-4">
        {activeTab === "all" && (
          <>
            {staff?.map((s: iStaffGetProps, index: number) => (
              <CustomCardWithHeader
                key={index}
                title={s?.fullName || "-"}
                isChip={true}
                chipText={s?.status ? "Active" : "Inactive"}
                chipColor={s?.status ? "success" : "danger"}
                className="hover:bg-secondary/5 transition-all duration-300 hover:shadow-md"
                mainContent={
                  <div className="-mt-4">
                    <div className="grid grid-cols-2 tablet:grid-cols-4 laptop:grid-cols-5 text-body gap-4">
                      <div className="flex flex-col ">
                        <p className="text-body">Email</p>
                        <p className="text-body-bold font-primary">
                          {s?.email}
                        </p>
                      </div>

                      <div className="flex flex-col ">
                        <p className="text-body">Contact No Type</p>
                        <p className="text-body-bold font-primary">
                          {s?.phoneNumber}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Address</p>
                        <p className="text-body-bold font-primary">
                          {addressFormatHelper({
                            addressLine1: s?.address,
                            city: s?.city,
                            state: s?.state,
                            country: s?.country,
                            postalCode: s?.zipCode,
                          }) || "-"}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Total Complete Jobs</p>
                        <p className="text-body-bold">
                          {s?.numberOfCompletedServices || "0"}/
                          {s?.serviceIds?.length || "0"}
                        </p>
                      </div>

                      <div className="flex flex-col">
                        <p className="text-body">Date of Registration</p>
                        <p className="text-body-bold">
                          {" "}
                          {moment(s?.createdAt).format(
                            "DD MMM YYYY, hh:mm A"
                          ) || "-"}
                        </p>
                      </div>
                    </div>

                    <div className="mt-3">
                      <CustomProgressBar
                        lable="Performance"
                        value={s?.numberOfCompletedServices || 0}
                        maxValue={s?.serviceIds?.length || 0}
                        showValueLabel={true}
                        color={handleProgressBarColor(
                          s?.serviceIds?.length || 0,
                          s?.numberOfCompletedServices
                        )}
                      />
                    </div>
                  </div>
                }
              />
            ))}
          </>
        )}

        {activeTab === "leave_calendar" && <StaffLeavePage />}
      </div>

      {activeTab != "leave_calendar" && (
        <div className="flex justify-end items-end py-5 mt-7">
          <CustomPagination
            page={currentPage}
            initialPage={1}
            total={totalPage}
            size="md"
            onChange={setCurrentPage}
            // itemPerPage={viewItemPerPage}
            // onItemPerPageChange={(value) => setViewItemPerPage(value)}
          />
        </div>
      )}
    </div>
  );
};

export default AllStaff;
