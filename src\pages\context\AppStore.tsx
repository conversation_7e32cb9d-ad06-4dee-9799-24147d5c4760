import { createContext, ReactNode, useContext, useState } from "react";
import { AppStoreType, defaultStore } from "./initialValue";

const AppStoreContext = createContext<AppStoreType>(defaultStore);

// Provider component
export const AppStoreProvider = ({ children }: { children: ReactNode }) => {
  const [serviceData, setServiceData] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [isDown, setDown] = useState<boolean>(false);
  const [bookingData, setBookingData] = useState<any[]>([]);

  return (
    <AppStoreContext.Provider
      value={{
        serviceData,
        setServiceData,
        userData,
        setUserData,
        isDown,
        setDown,
        bookingData,
        setBookingData,
      }}
    >
      {children}
    </AppStoreContext.Provider>
  );
};

// Custom hook to use the store
export const useAppStore = () => useContext(AppStoreContext);
