import { useEffect, useState } from "react";
import { DayPicker } from "react-day-picker";
import "react-day-picker/style.css";
import {
  IBookingProps,
  ITimeslotAvailabilityFetchProps,
  ITimeslotResponse,
} from "../../types";
import { useFetchServiceDataById } from "../../hooks/queries/useFetchData";
import { addToast, Divider } from "@heroui/react";
import { useCheckTimesSlotAvailability } from "../../hooks/mutations/usePostData";
import moment from "moment";
import SmallLoadingSpinner from "../../components/ui/SmallLoadingSpinner";
import NoDataFound from "../NoDataFound";
import { FaUser } from "react-icons/fa";
import CustomTextArea from "../../components/ui/CustomTextArea";
import CustomButton from "../../components/ui/CustomButton";

type weekType = {
  day: string;
  key: number;
};

type availabilityType = {
  id: string;
  alldate: false;
  day: string;
};

type StaffMember = {
  staffId: string;
  fullName: string;
  providerStaffId: string;
  isProvider: boolean;
};

// Type for a timeslot with staff
type TimeSlotType = {
  timeSlotId: string;
  from: string;
  to: string;
  availableStaff: StaffMember[];
};

const week: weekType[] = [
  { day: "sunday", key: 0 },
  { day: "monday", key: 1 },
  { day: "tuesday", key: 2 },
  { day: "wednesday", key: 3 },
  { day: "thursday", key: 4 },
  { day: "friday", key: 5 },
  { day: "saturday", key: 6 },
];

const BookingReshedule = (data: IBookingProps) => {
  const [selected, setSelected] = useState<Date>();
  const [serviceId, setServiceId] = useState<string>("");
  const [unAvailableDates, setUnAvailableDates] = useState<number[]>([0]);
  const [timeSlots, setTimeSlots] = useState<ITimeslotResponse[]>([]);
  const [selectTimeSlot, setSelectTimeSlot] = useState<string>("");
  const [selectedStaffId, setSelectedStaffId] = useState<string>("");
  const [availableStaff, setAvailableStaff] =
    useState<ITimeslotResponse | null>(null);

  console.log("Booking data: ", data);
  const {
    mutateAsync,
    isPending,
    isError: isCheckTimeslotError,
    error: checkTimeslotError,
  } = useCheckTimesSlotAvailability();
  const {
    data: service,
    isFetching: isServiceLoading,
    isError: isSearchError,
  } = useFetchServiceDataById(serviceId);

  const bookedDays = new Date(data?.appointmentDate);

  useEffect(() => {
    if (!data) return;
    setServiceId(data?.serviceId);
  }, [data]);

  //   HANDLE UN AVAILABLE DATES
  useEffect(() => {
    if (!service?.serviceInfo?.availability) return;

    const availability = service?.serviceInfo?.availability;

    const availableKeys = availability.map((a: availabilityType) => {
      return week.find((w: weekType) => w.day === a.day)?.key;
    });

    const unAvailableKeys = week
      .map((w) => w.key)
      .filter((key) => !availableKeys.includes(key));

    console.log("getUnMatchWeekNumber ", unAvailableKeys);
    setUnAvailableDates(unAvailableKeys);
  }, [service]);

  // CHECK AVAILABILITY TIMESLOT
  useEffect(() => {
    const checkBookingAvailableTimeSlot = async () => {
      if (!serviceId) return;
      if (!selected && !bookedDays) return;

      setSelectTimeSlot("");
      setAvailableStaff(null);
      setSelectedStaffId("");

      const payload: ITimeslotAvailabilityFetchProps = {
        serviceId,
        date: moment(selected || bookedDays).format("YYYY-MM-DD"),
      };

      console.log("PPPPPP: ", payload);

      try {
        const response = await mutateAsync(payload);
        if (response.success) {
          setTimeSlots(response.timeSlots);
        } else {
          setTimeSlots([]);
        }

        console.log("response: ", response);
        if (!response?.success) {
          addToast({
            title: "Error",
            description: response?.message || "Something went wrong",
            radius: "md",
            color: "danger",
          });
        }
      } catch (error) {
        console.log("Error check timeslot availability: ", error);
      }
    };

    checkBookingAvailableTimeSlot();
  }, [selected, mutateAsync, serviceId]);

  useEffect(() => {
    setSelectedStaffId("");
    const matchedSlot = timeSlots.find(
      (slot: ITimeslotResponse) => slot.timeSlotId === selectTimeSlot
    );

    setAvailableStaff(matchedSlot || null);
  }, [selectTimeSlot, timeSlots]);

  console.log("selected: ", selected);
  console.log("timeSlots: ", timeSlots);

  //   const bookedDayss = [
  //     new Date(2025, 8, 13),
  //     new Date(2025, 8, 19),
  //     new Date(2025, 8, 18),
  //     // new Date(2025, 8, 9),
  //     // new Date(2025, 11, 11),
  //   ];
  return (
    <div className="grid grid-cols-1 desktop:grid-cols-3 gap-4">
      <div>
        <DayPicker
          animate
          navLayout="around"
          numberOfMonths={1}
          modifiers={{
            booked: bookedDays,
            //   bookedDays: bookedDayss,
          }}
          modifiersClassNames={{
            booked: "my-booked-class",
            today: "calendar-today",
            //   bookedDays: "calendar-holiday",
            //   selected: "rounded-xl bg-blue-500 text-white",
          }}
          defaultMonth={bookedDays}
          // hidden={bookedDays}
          disabled={{ before: new Date(), dayOfWeek: unAvailableDates }}
          // disabled={{ before: new Date() }}
          // excludeDisabled
          mode="single"
          selected={selected}
          onSelect={setSelected}
          required
          // footer={
          //   selected
          //     ? `Selected: ${selected.toLocaleDateString()}`
          //     : "Pick a day."
          // }
        />
      </div>

      <div>
        <p className="text-body-bold mb-2">{`Appointment Time - (${moment(
          selected
        ).format("DD MMM YYYY")}) `}</p>

        <div className="grid tablet:grid-cols-3 desktop:grid-cols-3 gap-2 ">
          {isPending ? (
            <div className="col-span-3">
              <SmallLoadingSpinner size="md" variant="wave" />
            </div>
          ) : isCheckTimeslotError ? (
            <div className="col-span-3">
              <NoDataFound isIcon />
            </div>
          ) : (
            <>
              {timeSlots.map((time: ITimeslotResponse, index: number) => (
                <button
                  key={index}
                  onClick={() => setSelectTimeSlot(time?.timeSlotId)}
                  className={`flex justify-center items-center border border-secondary/30  p-2 shadow-sm  rounded-md w-38 ${
                    selectTimeSlot === time?.timeSlotId
                      ? "bg-secondary/40 shadow-md"
                      : "hover-primary-gradient"
                  }`}
                >
                  <p className="text-body-bold">{`${time?.from} - ${time?.to}`}</p>
                </button>
              ))}
            </>
          )}
        </div>
      </div>

      <div className=" ">
        <p className="text-body-bold mb-2">Staff</p>
        <div className="grid tablet:grid-cols-3 desktop:grid-cols-3 gap-2 ">
          {isPending ? (
            <div className="col-span-3">
              <SmallLoadingSpinner size="md" variant="wave" />
            </div>
          ) : isCheckTimeslotError ? (
            <div className="col-span-3">
              <NoDataFound isIcon />
            </div>
          ) : (
            <>
              {availableStaff?.availableStaff?.map((s: any, index: number) => (
                <button
                  key={index}
                  onClick={() => setSelectedStaffId(s?.staffId || "")}
                  className={`flex justify-start items-center border border-secondary/30  p-2 shadow-sm  rounded-md w-38 ${
                    selectedStaffId === s?.staffId
                      ? "bg-secondary/40"
                      : "hover-primary-gradient"
                  }`}
                >
                  <FaUser className="text-secondary" />
                  <p className="text-body-bold ml-2">{s?.fullName}</p>
                </button>
              ))}
            </>
          )}
        </div>
      </div>

      <div className="col-span-3">
        <CustomTextArea
          label="Reshedule Note"
          placeholder="Enter Note"
          minRows={2}
          description="Please provide a reason for reshedulng this booking. The customer will be notified so they understand the resheduling reason."
        />
      </div>

      <div className="flex justify-end items-end col-span-3 py-3">
        <CustomButton label="Reschedule" color="primary" />
      </div>
    </div>
  );
};

export default BookingReshedule;
