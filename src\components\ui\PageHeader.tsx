interface PageHeaderProps {
  title: string;
  description: string;
  components?: React.ReactNode;
}

const PageHeader = ({ title, description, components }: PageHeaderProps) => {
  return (
    // <div className="flex justify-between items-center mb-6">
    <div className="flex flex-col laptop:flex-row laptop:justify-between laptop:items-center gap-4 mb-6">
      <div className="flex flex-col">
        <h1 className="text-heading ">{title}</h1>
        <p className="text-body-mute">{description}</p>
      </div>

      <div>{components}</div>
      {/* <div className="flex-shrink-0">{components}</div> */}
    </div>
  );
};

export default PageHeader;
