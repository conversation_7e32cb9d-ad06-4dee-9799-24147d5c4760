import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Chip, Divider, Image } from "@heroui/react";
import {
  FaRegAddressCard,
  FaRegCircleUser,
  FaEnvelope,
  FaMobileAlt,
  FaBirthdayCake,
  FaLanguage,
  FaMoneyBillWave,
  FaMapMarkerAlt,
} from "react-icons/fa";
import { VscAccount } from "react-icons/vsc";
import { AiOutlineMail } from "react-icons/ai";
import { LuLockKeyhole } from "react-icons/lu";
import { SiFusionauth } from "react-icons/si";
import CustomChip from "../../components/ui/CustomChip";
import CustomButton from "../../components/ui/CustomButton";
import { MdOutlineLocalPhone } from "react-icons/md";
import ProviderVerifyModal from "./ProviderVerifyModal";
import EditProviderDetails from "./EditProviderDetails";
import { useFetchUserDetailsById } from "../../hooks/queries/useFetchData";
import { useAuth } from "react-oidc-context";
import {
  convertDateToReadble,
  convertTimeWithDateMonthAndTime,
} from "../../utils/convertTime";
import { BsCalendar2Date, BsFillCalendar2DateFill } from "react-icons/bs";
import { AppError, ErrorCode, LoggerService } from "../../logger/LoggerService";
import { testApi } from "../../api/user/apiUser";

// Reusable component for displaying a row of information
const InfoRow = ({
  icon,
  label,
  value,
}: {
  icon?: React.ReactNode;
  label: string;
  value: string;
}) => (
  <div className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-800 last:border-b-0">
    <div className="flex items-center gap-3 text-sm text-gray-500">
      {icon}
      <span className="text-body1">{label}</span>
    </div>
    <p className="text-body2 text-end">{value || "—"}</p>
  </div>
);

// Reusable component for security settings
const SecuritySettingRow = ({
  icon,
  title,
  description,
  action,
  status,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  action?: React.ReactNode;
  status?: React.ReactNode;
}) => (
  <div className="flex flex-initial justify-between items-center">
    <div>
      <div className="flex items-center rounded-xl py-4 w-full max-w-md">
        {/* Icon */}
        <div className="w-12 h-12 rounded-full bg-gray-100 dark:bg-black/60  flex items-center justify-center mr-4">
          {icon}
        </div>

        {/* Text Content */}
        <div className="flex-1">
          <h4 className="text-subtitle1 font-semibold text-gray-900">
            {title}
          </h4>
          <p className="text-caption2 text-gray-300 mt-[2px]">{description}</p>
        </div>

        {/* Chip */}
        <div className="ml-5">{status}</div>
      </div>
    </div>
    <div>{action}</div>
  </div>
);

const UserProfile = ({ user }: { user?: any }) => {
  const auth = useAuth();
  const [activeTab, setActiveTab] = useState("overview");

  const { data, isFetching, isError, error } = useFetchUserDetailsById(
    auth.user?.profile?.preferred_username || ""
  );
  console.log("Auth: ", auth.user?.profile);
  const userData = data?.user || {};
  const email = auth?.user?.profile?.email_verified === "true" ? true : false;
  console.log("isEmail: ", email);

  console.log("USER DATA: ", userData);

  const currentUser = user || {
    name: "Auto Mobile Service Center Pvt",
    profilePicture:
      "https://cdn.staging.gigmosaic.ca//common/fallback-image.png",
    providerId: "DS-6537",
    isVerified: true,
    plan: "Business Pro",
    about:
      "A passionate developer with a love for coding and technology. Committed to building high-quality, user-centric applications.",
    accountType: "Business",
    businessCategory: "Electrical",
    joinDate: "2024 Mar 04",
    dob: "1990-05-15",
    language: "English",
    currency: "USD",
    email: "<EMAIL>",
    mobile: "****** 567 890",
    address: "123 Innovation Drive, Suite 456, Tech City, USA",
    security: {
      accountVerified: true,
      accountVerifiedOn: "22 Sep 2023 at 10:30:55",
      emailVerified: true,
      emailVerifiedOn: "22 Sep 2023 at 10:30:55",
      passwordLastChanged: "22 Sep 2023, 10:30:55 AM",
      twoFactorEnabled: true,
    },
  };

  const title = "Account not verified";
  const description =
    "Your account is not verified yet. Please verify your account to access all features.";

  // const uploadPendingTitle = "Documents Under Review";
  // const description1 =
  //   "We are currently reviewing your submitted documents. You’ll be notified once the verification process is complete.";

  const verifyFailTitle = "Document Verification Failed";
  const verifyFailDescription =
    "We could not verify your uploaded documents. Please check that your files are clear, valid, and meet all the required specifications, then try uploading again.";

  const uploadPendingTitle = "Documents Under Review";
  const uploadPendingDescription =
    "We are currently reviewing your submitted documents. You’ll be notified once the verification process is complete.";

  const addressParts = [
    userData?.address?.postalCode,
    userData?.address?.addressLine1,
    userData?.address?.addressLine2,
    userData?.address?.city,
    userData?.address?.state,
    userData?.address?.country,
  ];

  const address = addressParts.filter(Boolean).join(", ");

  const handleLogin = () => {
    // window.location.href = `http://localhost:3010/api/auth/login`;
    window.location.href = `http://localhost:3010/api/v1/auth/login`;
    // window.location.href = `${API_URL}/auth/login`;
  };

  const getAlert = (userData: any) => {
    if (!userData) return null;

    // Case 1: Docs not uploaded
    if (
      !userData.ISVerified &&
      !userData.IsDocUploaded &&
      !userData.IsDocVerified // && !userData.IsDocReviewComplete
    ) {
      return {
        title: "Account not verified",
        description:
          "Your account is not verified yet. Please verify your account to access all features.",
        color: "warning",
        button: true,
      };
    }

    // Case 2: Docs uploaded, pending verification
    if (
      userData.IsDocUploaded &&
      !userData.IsDocVerified &&
      !userData.ISVerified // && userData.IsDocReviewComplete
    ) {
      return {
        title: "Documents Under Review",
        description:
          "We are currently reviewing your submitted documents. You’ll be notified once the verification process is complete.",
        color: "secondary",
        button: true,
      };
    }

    // Case 3: Docs failed verification
    if (
      userData.IsDocUploaded &&
      !userData.IsDocVerified &&
      !userData.ISVerified // && !userData.IsDocReviewComplete
    ) {
      return {
        title: "Document Verification Failed",
        description:
          "We could not verify your uploaded documents. Please check that your files are clear, valid, and meet all the required specifications, then try uploading again.",
        color: "danger",
        button: true,
      };
    }

    // Case 4: Docs verified but account not activated
    // if (userData.IsDocVerified && !userData.ISVerified) {
    //   return {
    //     title: "Account not verified",
    //     description:
    //       "Your account is not verified yet. Please verify your account to access all features.",
    //     color: "warning",
    //   };
    // }

    // Case 5: Verified
    if (userData.ISVerified) {
      return false;
    }

    return null;
  };

  const alertConfig = getAlert(userData);

  const test = async () => {
    try {
      const testData = {
        name: "lakshan",
        address: "Galle",
        age: "21 years old",
      };

      // if (testData) throw new Error("data is empty");
      LoggerService.info("Test API call success", {
        operation: "test",
        payload: testData,
        // responseData: res.data,
      });
      const res = await testApi(testData);
    } catch (err: any) {
      console.log(err);
      LoggerService.error(err.message, {
        operation: "test",
        payload: err, // or err.stack
      });
    }
  };

  return (
    <div className="desktop:px-32  ">
      {alertConfig && (
        <div className="flex items-center justify-center w-full mb-5">
          <Alert
            title={alertConfig.title}
            description={alertConfig.description}
            color={
              alertConfig.color as
                | "default"
                | "warning"
                | "secondary"
                | "danger"
                | "primary"
                | "success"
                | undefined
            }
            endContent={alertConfig.button ? <ProviderVerifyModal /> : ""}
          />
        </div>
      )}

      {/* Profile Header */}
      <Card className="p-6 flex flex-col laptop:flex-row justify-between items-start laptop:items-center gap-4 w-full rounded-lg shadow-sm">
        <div className="flex items-center gap-6">
          <Image
            alt="Profile picture"
            src={
              userData.profilePicture ||
              "https://cdn.staging.gigmosaic.ca/common/fallback-image.png"
            }
            className="w-24 h-24 rounded-full object-cover border-4 border-primary/20 shadow-md"
          />
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-heading2 font-bold text-gray-800">
                {userData.name}
              </h1>

              <CustomChip
                label={currentUser.isVerified ? "Business Pro" : "Not Verified"}
                color={currentUser.isVerified ? "warning" : "danger"}
                size="md"
                variant="dot"
              />
            </div>
            <p className="text-body1 mt-2">
              Provider ID: {userData.userId || "-"}
            </p>

            <CustomChip
              label={userData.ISVerified ? "Verified" : "Not Verified"}
              color={userData.ISVerified ? "success" : "danger"}
              size="md"
              className="mt-2"
            />
          </div>
        </div>
        <CustomButton color="primary" variant="solid" size="md" onPress={test}>
          New login
        </CustomButton>
      </Card>

      {/* Tab Navigation */}
      <div className="mt-8">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-6">
            <button
              onClick={() => setActiveTab("overview")}
              className={`py-3 px-1 text-sm font-medium ${
                activeTab === "overview"
                  ? "border-b-2 border-primary text-primary"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab("security")}
              className={`py-3 px-1 text-sm font-medium ${
                activeTab === "security"
                  ? "border-b-2 border-primary text-primary"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Security
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === "overview" && (
            <div className="grid grid-cols-1 laptop:grid-cols-5 gap-6">
              {/* Left Column */}
              <div className="laptop:col-span-3">
                <Card className="p-6 rounded-lg shadow-sm">
                  <div className="flex flex-initial gap-5">
                    <h2 className="text-heading3 mb-2">About</h2>
                    <div className="mt-1">
                      <EditProviderDetails />
                    </div>
                  </div>
                  <p className="text-body1 text-gray-500 leading-relaxed">
                    {userData.bio || "-"}
                  </p>
                </Card>
                <Card className="p-6 mt-6 rounded-lg shadow-sm">
                  <div className="flex flex-initial gap-5">
                    <h2 className="text-heading3 mb-2">Contact Information</h2>
                    <div className="mt-1">
                      <EditProviderDetails />
                    </div>
                  </div>

                  <InfoRow
                    icon={<FaEnvelope />}
                    label="Email Address"
                    value={userData.email || "-"}
                  />
                  <InfoRow
                    icon={<MdOutlineLocalPhone />}
                    label="Contact Number"
                    value={userData.mobile || "-"}
                  />
                  <InfoRow
                    icon={<FaMapMarkerAlt />}
                    label="Address"
                    value={address || "-"}
                  />
                </Card>
              </div>

              {/* Right Column */}
              <div className="laptop:col-span-2">
                <Card className="p-6 rounded-lg shadow-sm">
                  <div className="flex flex-initial gap-5">
                    <h2 className="text-heading3 mb-2">Basic Information</h2>
                    <div className="mt-1">
                      <EditProviderDetails />
                    </div>
                  </div>
                  <InfoRow
                    label="Account Type"
                    value={
                      userData?.IsBusiness && !userData?.IsIndividual
                        ? "Business"
                        : userData?.IsIndividual && !userData?.IsBusiness
                        ? "Individual"
                        : "-"
                    }
                  />

                  <InfoRow
                    label="Business Category"
                    value={userData?.businessCategory || "-"}
                  />
                  <InfoRow
                    label="Date of Join "
                    value={convertDateToReadble(userData.createdAt) || "-"}
                  />
                  {userData?.IsBusiness === false && (
                    <InfoRow
                      icon={<BsFillCalendar2DateFill />}
                      label="Date of Birth"
                      value={convertDateToReadble(userData.dateOfBirth) || "-"}
                    />
                  )}
                  <InfoRow
                    icon={<FaLanguage />}
                    label="Language"
                    value={userData.language || "-"}
                  />
                  <InfoRow
                    icon={<FaMoneyBillWave />}
                    label="Currency"
                    value={userData.currencyCode || "-"}
                  />
                </Card>
              </div>
            </div>
          )}

          {activeTab === "security" && (
            <Card className="p-6 rounded-lg shadow-sm">
              <h2 className="text-heading3 mb-2">Security Settings</h2>
              <SecuritySettingRow
                icon={
                  <VscAccount
                    size={20}
                    className="text-2xl text-black/60 dark:text-white/60"
                  />
                }
                title="Account Verification"
                description={`Verified on ${currentUser.security.accountVerifiedOn}`}
                status={
                  <CustomChip
                    label={userData?.ISVerified ? "Verified" : "Not Verified"}
                    color={userData?.ISVerified ? "success" : "danger"}
                    size="sm"
                  ></CustomChip>
                }
              />
              <SecuritySettingRow
                icon={
                  <AiOutlineMail
                    size={20}
                    className="text-2xl text-black/60 dark:text-white/60"
                  />
                }
                title="Email Verification"
                description={`Verified on ${convertTimeWithDateMonthAndTime(
                  userData.createdAt
                )}`}
                status={
                  <CustomChip
                    label={email ? "Verified" : "Not Verified"}
                    color={email ? "success" : "danger"}
                    size="sm"
                  ></CustomChip>
                }
                // action={
                //   <Button variant="ghost" size="sm">
                //     {userData?.ISVerified ? "Change" : "Verify"}
                //   </Button>
                // }
              />
              <SecuritySettingRow
                icon={
                  <LuLockKeyhole
                    size={20}
                    className="text-2xl text-black/60 dark:text-white/60"
                  />
                }
                title="Password"
                description={`Last changed ${currentUser.security.passwordLastChanged}`}
                action={
                  <Button variant="ghost" size="sm">
                    Change Password
                  </Button>
                }
              />
              <SecuritySettingRow
                icon={
                  <SiFusionauth
                    size={20}
                    className="text-2xl text-black/60 dark:text-white/60"
                  />
                }
                title="Two-Factor Authentication"
                description={
                  userData?.ISVerified
                    ? "Your account is protected"
                    : "Add an extra layer of security"
                }
                status={
                  <CustomChip
                    label={userData?.ISVerified ? "Enabled" : "Disabled"}
                    color={userData?.ISVerified ? "success" : "warning"}
                    size="sm"
                  ></CustomChip>
                }
                action={
                  <Button variant="ghost" size="sm">
                    {userData?.ISVerified ? "Disable" : "Enable"}
                  </Button>
                }
              />
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
