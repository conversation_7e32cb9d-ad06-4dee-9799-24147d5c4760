// logger.ts
import pino from "pino";

/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = "debug",
  INFO = "info",
  WARN = "warn",
  ERROR = "error",
}

/**
 * Industrial-style error codes
 */
export enum ErrorCode {
  NETWORK_TIMEOUT = "NETWORK_TIMEOUT",
  NETWORK_OFFLINE = "NETWORK_OFFLINE",
  API_BAD_REQUEST = "API_BAD_REQUEST",
  API_UNAUTHORIZED = "API_UNAUTHORIZED",
  API_NOT_FOUND = "API_NOT_FOUND",
  API_SERVER_ERROR = "API_SERVER_ERROR",
  VALIDATION_FAILED = "VALIDATION_FAILED",
  AUTH_EXPIRED = "AUTH_EXPIRED",
  UI_RENDER_ERROR = "UI_RENDER_ERROR",
  UNKNOWN = "UNKNOWN",
}

/**
 * Context info for logs
 */
export interface LogContext {
  userId?: string;
  route?: string;
  operation?: string; // API, mutation, component
  method?:
    | "GET"
    | "POST"
    | "PUT"
    | "DELETE"
    | "PATCH"
    | "HEAD"
    | "OPTIONS"
    | "undefined";
  domain?: string;
  baseURL?: string;
  payload?: unknown;
  responseData?: unknown;
  userAgent?: string;
  errorStack?: unknown;
  code?: ErrorCode;
  requestId?: string; // unique ID per request
  sessionId?: string; // user session
  env?: string; // "staging" | "production"
  appVersion?: string; // frontend version
  service?: string;
}

/**
 * Custom Error wrapper
 */
export class AppError extends Error {
  code: ErrorCode;
  status?: number;
  context?: LogContext;

  constructor(
    code: ErrorCode,
    message: string,
    status?: number,
    context?: LogContext
  ) {
    super(message);
    this.code = code;
    this.status = status;
    this.context = context;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

/**
 * Pino instance
 */

//Export to aws cognito
const logger = pino({
  level: process.env.NODE_ENV === "production" ? "info" : "debug",
  browser: { asObject: true }, // always structured JSON logs
  transport: {
    target: "pino-pretty",
    options: {
      colorize: true,
      translateTime: "SYS:yyyy-mm-dd HH:MM:ss.l",
    },
  },
});

const DEFAULT_DOMAIN =
  typeof window !== "undefined" ? window.location.hostname : "frontend";

const ENV = import.meta.env.VITE_NODE_ENV || "development";
const APP_VERSION = import.meta.env.VITE_APP_VERSION || "1.0.0";
/**
 * LoggerService
 */
export class LoggerService {
  private static enrichContext(context?: LogContext): LogContext {
    return {
      domain: DEFAULT_DOMAIN,
      //   userId:
      //     context?.userId ??
      //     (typeof window !== "undefined"
      //       ? localStorage.getItem("userId")
      //       : undefined),
      env: ENV,
      appVersion: APP_VERSION,
      sessionId: "5372677273",
      userId: "G_TE-356",
      userAgent: navigator.userAgent,
      route:
        context?.route ??
        (typeof window !== "undefined" ? window.location.pathname : undefined),
      ...context,
    };
  }

  private static format(
    message: string,
    context?: LogContext,
    code?: ErrorCode
  ) {
    return {
      msg: message,
      context: this.enrichContext(context),
      code: code,
      timestamp: new Date().toISOString(),
    };
  }

  static debug(message: string, context?: LogContext) {
    const log = this.format(message, context);
    logger.debug(log);
  }

  static info(message: string, context?: LogContext) {
    const log = this.format(message, context);
    logger.info(log);
  }

  static warn(message: string, context?: LogContext) {
    const log = this.format(message, context);
    logger.warn(log);
  }

  static error(message: string, context?: LogContext, code?: ErrorCode) {
    const log = this.format(message, context, code);
    logger.error(log);
  }

  static success(message: string, context?: LogContext) {
    const log = this.format(message, context);
    logger.info({ ...log, level: "success" }); // success is just an info variant
  }
}

/**
 * Global listeners for unhandled errors
 */
if (typeof window !== "undefined") {
  window.addEventListener("error", (event) => {
    LoggerService.error(
      "Unhandled UI error",
      { errorStack: event.error },
      ErrorCode.UI_RENDER_ERROR
    );
  });

  window.addEventListener("unhandledrejection", (event) => {
    LoggerService.error(
      "Unhandled Promise rejection",
      { errorStack: event.reason },
      ErrorCode.UNKNOWN
    );
  });
}
