interface CommonReplyProps {
  title?: string;
  icon?: React.ReactNode;
  description?: string;
}

const CommonReply = ({ title, icon, description }: CommonReplyProps) => {
  return (
    <div className="p-2 rounded-lg border-l-4 border-secondary bg-primary/10 mt-3">
      <div className="flex items-center gap-2 mb-2">
        {icon}

        <span className="text-caption">{title}</span>
      </div>
      <p className="text-body ">{description}</p>
    </div>
  );
};

export default CommonReply;
