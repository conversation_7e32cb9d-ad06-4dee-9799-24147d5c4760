// import axios from "axios";
// import { getToken } from "./tokenProvider";
// import { AppError, <PERSON>rrorCode, LoggerService } from "../../logger/LoggerService";
// // import { useNavigate } from "react-router-dom";

// export const apiClient = axios.create({
//   baseURL: import.meta.env.VITE_BACKEND_PORT,
//   timeout: 5000,
//   headers: {
//     "Content-Type": "application/json",
//   },
//   // withCredentials: true,
// });

// apiClient.interceptors.request.use(
//   (config) => {
//     // const navigate = useNavigate();
//     const token = getToken();
//     if (token) {
//       console.log(token);
//       // console.log("Tk: ", true);
//     } else {
//       // console.log("Tk: ", false);
//     }

//     if (token) {
//       config.headers["Authorization"] = `Bearer ${token} `;
//     } else {
//       // window.location.href = "/staff/all-staff";
//     }
//     return config;
//   },
//   (error) => {
//     console.log("axios interceptor error: ", error);
//     return Promise.reject(error);
//   }
// );

// apiClient.interceptors.response.use(
//   (response) => {
//     // ✅ Success logging
//     // LoggerService.success("API call success", {
//     //   route: response.config.url,
//     //   method: response.config.method?.toUpperCase(),
//     //   baseURL: response.config.baseURL,
//     //   payload: response.config.data,
//     //   responseData: response.data,
//     //   operation: response.config.operation,
//     // });

//     return response;
//   },
//   (error) => {
//     const config = error.config || {};

//     console.log("Config: ", error);

//     // Wrap in AppError
//     const appError = new AppError(
//       ErrorCode.API_SERVER_ERROR,
//       error.response?.data?.message || error.message || "API request failed",
//       error.response?.status,
//       {
//         route: config.url,
//         operation: config.operation,
//         baseURL: config.baseURL,
//         method: config.method?.toUpperCase(),
//         payload: config.data,
//         errorStack: {
//           message: error.message,
//           name: error.name,
//           stack: error.stack,
//           code: error.code,
//           status: error.response?.status,
//           responseData: error.response?.data,
//         },
//       }
//     );

//     // // Log structured error
//     LoggerService.error(appError.message, appError.context, appError.code);

//     return Promise.reject(appError);
//   }
// );

// apiClient.ts
import axios from "axios";
import { getToken } from "./tokenProvider";
import { AppError, ErrorCode, LoggerService } from "../../logger/LoggerService";
import { addToast } from "@heroui/react";

export const apiClient = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_PORT,
  timeout: 5000,
  headers: { "Content-Type": "application/json" },
});

apiClient.interceptors.request.use(
  (request) => {
    const token = getToken();
    console.log(token);
    if (token) request.headers["Authorization"] = `Bearer ${token}`;

    LoggerService.info("API Request initiated", {
      route: request.url,
      baseURL: request.baseURL,
      payload: request.data,
    });

    return request;
  },
  (error) => {
    LoggerService.error(
      "Axios request error",
      {
        errorStack: {
          message: error.message,
          name: error.name,
          stack: error.stack,
        },
        route: error.request?.url,
        method: error.request?.method?.toUpperCase(),
        baseURL: error.request?.baseURL,
      },
      ErrorCode.UNKNOWN
    );

    return Promise.reject(error);
  }
);

// Response interceptor: log success + wrap errors
apiClient.interceptors.response.use(
  (response) => {
    LoggerService.success("API call success", {
      route: response.config.url,
      method: response.config.method
        ? (response.config.method.toUpperCase() as
            | "GET"
            | "POST"
            | "PUT"
            | "DELETE"
            | "PATCH"
            | "HEAD"
            | "OPTIONS")
        : undefined,
      baseURL: response.config.baseURL,
      payload: response.config.data,
      responseData: response.data,
    });

    return response;
  },
  (error) => {
    const config = error.config || {};

    console.log("Config: ", error);

    // Wrap in AppError
    const appError = new AppError(
      ErrorCode.API_SERVER_ERROR,
      error.response?.data?.message ||
        error.message ||
        "Unexpected API request failed",
      error.response?.status,
      {
        route: config.url,
        operation: config.operation,
        baseURL: config.baseURL,
        method: config.method?.toUpperCase(),
        payload: config.data,
        responseData: error.response?.data,
        errorStack: {
          message: error.message,
          name: error.name,
          contentType: error.response?.headers["content-type"],
          code: error.code,
          status: error.status,
        },
      }
    );

    if (error?.status === 503) {
      addToast({
        title: "Service Unavailable",
        description:
          "The server is temporarily unavailable. Please try again later. Error code: 503",
        color: "danger",
      });
    }

    LoggerService.error(appError.message, appError.context, appError.code);

    return Promise.reject(appError);
  }
);
