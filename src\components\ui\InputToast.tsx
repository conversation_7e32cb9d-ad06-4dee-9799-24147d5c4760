import Swal from "sweetalert2";

const InputToast = async ({
  title,
  placeholder,
  subText,
  confirmText,
  cancelText,
  showCancelBtn = true,
  minValue = 0,
  maxValue = 100,
}: {
  title: string;
  subText?: string;
  placeholder: string;
  showCancelBtn?: boolean;
  confirmText: string;
  cancelText: string;
  minValue?: number;
  maxValue?: number;
}): Promise<string | null> => {
  const result = await Swal.fire({
    input: "textarea",
    icon: "warning",
    title: title,
    text: subText,
    inputPlaceholder: placeholder,
    cancelButtonText: cancelText,
    confirmButtonText: confirmText,
    showCancelButton: showCancelBtn,
    inputAttributes: {
      "aria-label": placeholder,
    },
    customClass: {
      input: "text-body p-2",
      icon: "text-sm",
      popup: "rounded-lg shadow-xl  py-5",
      htmlContainer: "text-sm text-gray-600",
      cancelButton:
        "bg-gray-300 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none w-24",
      confirmButton:
        "bg-primary px-4 py-2 rounded-md hover:bg-secondary focus:outline-none ml-5 w-24",
      title: "text-title ",
    },
    inputValidator: (value) => {
      if (!value) {
        return "Reson is required";
      }
      if (value.length < minValue) {
        return `Please enter a reson more than ${minValue} characters`;
      }
      if (value.length > maxValue) {
        return `Please enter a reson less than ${maxValue} characters`;
      }
    },
  });

  return result.value;
};

export default InputToast;
