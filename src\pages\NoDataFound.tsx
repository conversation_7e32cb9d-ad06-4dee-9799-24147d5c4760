import img from "../assets/no-results.png";

interface notFoundProps {
  isIcon?: boolean;
  text?: string;
}

const NoDataFound = ({ isIcon, text }: notFoundProps) => {
  return (
    <div className="flex flex-col justify-center items-center min-h-[60vh] ">
      {isIcon && <img src={img} alt="no results" className="w-14" />}
      <p className="text-body mt-1">{text || "Result Not Found"}</p>
    </div>
  );
};

export default NoDataFound;
