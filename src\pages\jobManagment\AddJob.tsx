import CustomButton from "../../components/ui/CustomButton";
import {
  addToast,
  <PERSON><PERSON>,
  Modal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  ModalHeader,
  useDisclosure,
} from "@heroui/react";
import {
  addressFormatHelper,
  multipleFileUplaodHelper,
} from "../../utils/common";
import CustomCardWithHeader from "../../components/ui/CustomCardWithHeader";
import moment from "moment";
import { useState } from "react";
import { IoMdClose } from "react-icons/io";
import { FormProvider, useForm } from "react-hook-form";
import CustomTextArea from "../../components/ui/CustomTextArea";
import { addJobSchema } from "../../validation/addJobSchema";
import { yupResolver } from "@hookform/resolvers/yup";
import { AddJobDefaultValues } from "../../types";
import { useCreateJobCardForDisplayToProfile } from "../../hooks/mutations/usePostData";
import { FaBedPulse } from "react-icons/fa6";

let defaultValues: AddJobDefaultValues = {
  packageId: "",
  bookingId: "",
  serviceId: "",
  providerId: "",
  images: [],
  description: "",
};

const AddJob = ({ data }: any) => {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [operationalError, setOperationalError] = useState<string>("");
  const {
    mutate,
    isError,
    error: apiError,
    reset: resetMutation,
  } = useCreateJobCardForDisplayToProfile();
  console.log("b: ", data);

  if (!data) return null;

  const form = useForm({
    defaultValues: defaultValues,
    shouldUnregister: true,
    resolver: yupResolver(addJobSchema),
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const {
    register,
    setValue,
    watch,
    reset,
    trigger,
    clearErrors,
    formState: { errors },
  } = form;

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setUploadedFiles((prev) => [...prev, ...files]);
    setValue("images", [...uploadedFiles, ...files]);
    trigger("images");

    // Clear the input value to allow re-uploading the same file if needed
    event.target.value = "";
  };

  const removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
    const updatedFiles = uploadedFiles.filter((_, i) => i !== index);
    setValue("images", updatedFiles);
    trigger("images");
  };

  const heandleOpen = () => {
    onOpen();
    resetMutation();
    reset(defaultValues);
    setOperationalError("");
    clearErrors();
    setValue("packageId", data?.packageId || "");
    setValue("bookingId", data?.bookingId || "");
    setValue("serviceId", data?.serviceId || "");
    setValue("providerId", data?.bookingId || "");
    setValue("images", []);
    setUploadedFiles([]);
  };

  // console.log("errors: ", errors);
  // console.log("errors_api: ", apiError);
  // console.log("watch: ", watch());

  // const title = "This is an alert";
  // const description = "Thanks for subscribing to our newsletter!";

  const onSubmit = async (formData: any) => {
    setisLoading(true);
    console.log("Form Data: ", formData);
    console.log("Uploaded Files: ", uploadedFiles);
    try {
      const nestedFilePath = `${data?.serviceTitle}-${Date.now()}`;
      const generateProviderFolder = `${data?.providerId}`;
      const files = formData.images as File[];

      const galleryImageUrls = await multipleFileUplaodHelper({
        files,
        baseFolder: "provider",
        mainFolder: "bookings",
        subFolder: generateProviderFolder,
        nestedPath: nestedFilePath,
        errorMessageType: "Image",
      });

      if (!galleryImageUrls || galleryImageUrls.length === 0) {
        addToast({
          title: "Image upload",
          description:
            "Uplaod failed. try again. Please contact support if the issue persists.",
          radius: "md",
          color: "danger",
        });
        setOperationalError(
          "Image upload failed. Please try again.Please contact support if the issue persists. "
        );
        return;
      }
      const payload = {
        ...formData,
        images: galleryImageUrls,
      };

      console.log("Payload: ", payload);

      mutate(payload);
    } catch (error) {
      console.error("Error submitting form: ", error);
      setOperationalError(
        "An error occurred while submitting the form. Please try again."
      );
    } finally {
      setisLoading(false);
    }
  };

  return (
    <>
      <CustomButton
        label="Add to Profile"
        type="button"
        size="sm"
        color="primary"
        // startContent={<MdAddCircleOutline size={20} />}
        onPress={heandleOpen}
        className="-mt-6 sm:mt-0"
      />
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        size="5xl"
        scrollBehavior="inside"
      >
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <ModalContent>
              {(onClose) => (
                <>
                  <ModalHeader className="flex flex-col gap-1">
                    <p>Complete Job</p>
                  </ModalHeader>
                  <ModalBody>
                    <div>
                      {(isError || operationalError) && (
                        <div className="flex items-center justify-center w-full mb-2">
                          <Alert
                            description={operationalError || apiError?.message}
                            title="Error occurred"
                            color="danger"
                            variant="faded"
                          />
                        </div>
                      )}

                      <CustomCardWithHeader
                        title="Booking Summary"
                        subtitle="  Add photos, videos, and completion details"
                        isHeaderDevider
                        className=" hover:shadow-md "
                        mainContent={
                          <>
                            <div className="-mt-3">
                              <div className="grid grid-cols-5 gap-4">
                                <div className="flex flex-col ">
                                  <p className="text-body">Booking ID</p>
                                  <p className="text-body-bold font-primary">
                                    {data?.referenceCode || "-"}
                                  </p>
                                </div>

                                <div className="flex flex-col ">
                                  <p className="text-body">Booking ID</p>
                                  <p className="text-body-bold font-primary">
                                    {data?.serviceName || "-"}
                                  </p>
                                </div>

                                <div className="flex flex-col ">
                                  <p className="text-body">Booking Date</p>
                                  <p className="text-body-bold font-primary">
                                    {moment(data.appointmentDate).format(
                                      "DD MMM YYYY"
                                    ) || "-"}
                                  </p>
                                </div>

                                <div className="flex flex-col">
                                  <p className="text-body">Appointment Time</p>
                                  <p className="text-body-bold font-primary">
                                    {data?.appointmentTimeFrom ||
                                    data?.appointmentTimeTo
                                      ? `${
                                          data?.appointmentTimeFrom || "-"
                                        } - ${data?.appointmentTimeTo || "-"}`
                                      : "-"}
                                  </p>
                                </div>

                                <div className="flex flex-col">
                                  <p className="text-body">Total</p>
                                  <p className="text-body-bold">
                                    ${data?.total || "-"}
                                  </p>
                                </div>

                                <div className="flex flex-col">
                                  <p className="text-body">Customer</p>
                                  <p className="text-body-bold">
                                    {`${data?.personalInfo?.firstName} ${data?.personalInfo?.lastName}`}
                                  </p>
                                </div>

                                <div className="flex flex-col">
                                  <p className="text-body">Address</p>
                                  <p className="text-body-bold">
                                    {addressFormatHelper(
                                      data?.personalInfo?.address
                                    )}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </>
                        }
                      />

                      <div className="grid grid-cols-2 gap-3 mt-3">
                        {/* Photo*/}
                        <CustomCardWithHeader
                          title="Photos"
                          subtitle="  Add photos, videos"
                          isHeaderDevider
                          className=" hover:shadow-md "
                          mainContent={
                            <>
                              <div className="-mt-3 gap-3 flex flex-col">
                                <ul className="list-disc pl-4 text-caption ">
                                  <li className="text-caption">
                                    Maximum 5 images allowed
                                  </li>
                                  <li className="text-caption">
                                    Minimum image size is 500px x 300px and
                                    maximum file size is 5mb.
                                  </li>
                                </ul>
                                <div className="max-w-md mx-full">
                                  <div
                                    className={`rounded-md border-2 border-dashed ${
                                      errors?.images
                                        ? "border-red-500 bg-red-50"
                                        : uploadedFiles?.length === 5
                                        ? "border-gray-200 bg-gray-50"
                                        : "border-secondary/50 bg-secondary/5"
                                    }`}
                                  >
                                    {/* Make the whole drop zone clickable via <label> */}
                                    <label
                                      htmlFor="chooseFile"
                                      className={` ${
                                        uploadedFiles?.length === 5
                                          ? "cursor-not-allowed"
                                          : "cursor-pointer"
                                      }  p-4 min-h-[100px] flex flex-col items-center justify-center text-center `}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className={` ${
                                          uploadedFiles?.length === 5
                                            ? " fill-gray-300"
                                            : "  fill-gray-600"
                                        }  w-8 mb-4 inline-block `}
                                        viewBox="0 0 32 32"
                                      >
                                        <path d="M23.75 11.044a7.99 7.99 0 0 0-15.5-.009A8 8 0 0 0 9 27h3a1 1 0 0 0 0-2H9a6 6 0 0 1-.035-12 1.038 1.038 0 0 0 1.1-.854 5.991 5.991 0 0 1 11.862 0A1.08 1.08 0 0 0 23 13a6 6 0 0 1 0 12h-3a1 1 0 0 0 0 2h3a8 8 0 0 0 .75-15.956z" />
                                        <path d="M20.293 19.707a1 1 0 0 0 1.414-1.414l-5-5a1 1 0 0 0-1.414 0l-5 5a1 1 0 0 0 1.414 1.414L15 16.414V29a1 1 0 0 0 2 0V16.414z" />
                                      </svg>

                                      <h4
                                        className={` ${
                                          uploadedFiles?.length === 5
                                            ? "text-gray-300 text-[14px]"
                                            : " text-body"
                                        }  `}
                                      >
                                        Drag & Drop or{" "}
                                        <span
                                          className={` ${
                                            uploadedFiles?.length === 5
                                              ? "text-gray-300 text-xs underline"
                                              : "text-secondary underline"
                                          }  `}
                                        >
                                          Choose file
                                        </span>{" "}
                                        to upload
                                      </h4>
                                    </label>

                                    {/* File input (hidden) */}

                                    <input
                                      type="file"
                                      id="chooseFile"
                                      className="hidden"
                                      accept="image/*"
                                      max={2}
                                      multiple
                                      onChange={handleFileUpload}
                                      disabled={uploadedFiles?.length === 5}
                                    />
                                  </div>
                                  {errors?.images && (
                                    <p className="text-xs text-red-400 mt-1">
                                      {errors?.images?.message}
                                    </p>
                                  )}
                                </div>

                                {/* Uploaded Files Preview */}
                                {uploadedFiles.length > 0 && (
                                  <div className="space-y-2 mt-2">
                                    <p className="text-caption">
                                      Uploaded Files ({uploadedFiles.length})
                                    </p>
                                    <div className="grid grid-cols-3 gap-2">
                                      {uploadedFiles.map((file, index) => (
                                        <div
                                          key={index}
                                          className="relative group"
                                        >
                                          <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                                            {file.type.startsWith("image/") ? (
                                              //   <Camera className="h-6 w-6 text-muted-foreground" />
                                              <img
                                                src={URL.createObjectURL(file)}
                                              />
                                            ) : (
                                              <></>
                                              //   <Video className="h-6 w-6 text-muted-foreground" />
                                            )}
                                          </div>
                                          <CustomButton
                                            size="sm"
                                            isIconOnly
                                            variant="flat"
                                            radius="none"
                                            color="danger"
                                            startContent={
                                              <IoMdClose
                                                size={18}
                                                className="text-red-500"
                                              />
                                            }
                                            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0  group-hover:opacity-100 transition-opacity"
                                            onPress={() => removeFile(index)}
                                          />
                                          <p className="text-xs text-center mt-1 truncate">
                                            {file.name}
                                          </p>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </>
                          }
                        />

                        {/* Completion Details*/}
                        <CustomCardWithHeader
                          title="Completion Details"
                          subtitle="Add completion details"
                          isHeaderDevider
                          className=" hover:shadow-md "
                          mainContent={
                            <>
                              <div className="-mt-3">
                                <CustomTextArea
                                  label="Work Completed Notes"
                                  placeholder="Enter work completed notes"
                                  isClearable
                                  maxRows={20}
                                  minRows={5}
                                  {...register("description")}
                                  errorMessage={
                                    errors.description?.message as string
                                  }
                                  isInvalid={!!errors.description}
                                />
                              </div>
                            </>
                          }
                        />
                      </div>
                    </div>
                  </ModalBody>
                  <ModalFooter>
                    <CustomButton
                      color="danger"
                      variant="light"
                      onPress={() => {
                        onClose();
                        reset();
                      }}
                      label="Close"
                    />
                    <CustomButton
                      type="submit"
                      label="Submit"
                      color="primary"
                      isLoading={isLoading}
                    />
                  </ModalFooter>
                </>
              )}
            </ModalContent>
          </form>
        </FormProvider>
      </Modal>
    </>
  );
};

export default AddJob;
