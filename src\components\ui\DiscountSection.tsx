import { DatePicker, DateRangePicker, Radio, RadioGroup } from "@heroui/react";
import { Controller, useFormContext } from "react-hook-form";
import CustomInput from "./CustomInput";
import CustomNumberInput from "./CustomNumberInput";
import { BiDollar } from "react-icons/bi";
import { FaUser } from "react-icons/fa";
import { getLocalTimeZone, parseDate, today } from "@internationalized/date";
import { convertToInternationalizedDateTimeToReadble } from "../../utils/convertTime";
import { memo } from "react";
import { IPromotionSumbit } from "../../types";

const DiscountSection = memo(() => {
  const {
    register,
    watch,
    control,
    formState: { errors },
  } = useFormContext<IPromotionSumbit>();

  const isDiscount = watch("isDiscount");
  const isPackage = watch("isPackage");
  const valueType = watch("discount.valueType");
  const durationType = watch("discount.durationType");
  const discountType = watch("discount.discountType");
  const promoCode = watch("discount.promoCode");

  const duration = watch("discount.duration");

  console.log("PROMOTION new: ", watch());
  console.log("Watch duration: ", duration);
  console.log("Watch valueType: ", valueType);
  console.log("Watch durationType: ", durationType);
  console.log("Watch discountType: ", discountType);

  return (
    <>
      {/* DISCOUNT  */}
      <div className="">
        <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-5 mt-3">
          {!isPackage && (
            <div className="flex flex-col">
              {/* DISCOUNT DURATION TYPE */}
              <div
                className={`${
                  errors?.discount?.discountType
                    ? "border-red-500 bg-red-50 dark:border-red-500"
                    : ""
                } rounded-lg p-3 border border-secondary/30 cursor-pointer shadow-sm primary-gradient`}
              >
                <Controller
                  name="discount.discountType"
                  control={control}
                  defaultValue="general-discount"
                  render={({ field }) => (
                    <>
                      <RadioGroup
                        label="Discount Type"
                        orientation="horizontal"
                        classNames={{
                          label: "text-body-bold",
                        }}
                        defaultValue="general-discount"
                        value={discountType}
                        onValueChange={(val: string) => {
                          field.onChange(val);
                          if (val === "promo-code") {
                            // resetField("");
                          }
                        }}
                        isInvalid={!!errors?.discount?.discountType}
                        // isInvalid={true}
                      >
                        <Radio
                          classNames={{
                            label: "text-body",
                          }}
                          value="general-discount"
                          size="sm"
                          className="mr-3"
                        >
                          General Discount
                        </Radio>
                        <Radio
                          classNames={{
                            label: "text-body",
                          }}
                          value="promo-code"
                          size="sm"
                        >
                          Promo Code
                        </Radio>
                      </RadioGroup>
                    </>
                  )}
                />
              </div>
              {errors?.discount?.discountType && (
                <span className="text-error">
                  {errors?.discount?.discountType.message}
                </span>
              )}
            </div>
          )}

          {/* DISCOUNT TYPE */}
          <div className=" rounded-lg p-3 border border-secondary/30 cursor-pointer shadow-sm primary-gradient">
            <Controller
              name="discount.valueType"
              control={control}
              defaultValue="percentage"
              render={({ field }) => (
                <>
                  <RadioGroup
                    label="Value Type"
                    orientation="horizontal"
                    classNames={{
                      label: "text-body-bold",
                    }}
                    defaultValue="percentage"
                    value={valueType}
                    onValueChange={(val: string) => {
                      field.onChange(val);
                    }}
                    isInvalid={!!errors?.discount?.valueType}
                  >
                    <Radio
                      classNames={{
                        label: "text-body",
                      }}
                      value="amount"
                      size="sm"
                      className="mr-3"
                    >
                      Amount
                    </Radio>
                    <Radio
                      classNames={{
                        label: "text-body",
                      }}
                      value="percentage"
                      size="sm"
                    >
                      Percentage
                    </Radio>
                  </RadioGroup>
                </>
              )}
            />
            {errors?.discount?.valueType && (
              <span className="text-error">
                {errors?.discount?.valueType?.message}
              </span>
            )}
          </div>

          {/* DISCOUNT DURATION TYPE */}
          <div className=" rounded-lg p-3 border border-secondary/30 cursor-pointer shadow-sm primary-gradient">
            <Controller
              name="discount.durationType"
              control={control}
              defaultValue="life-time"
              render={({ field }) => (
                <>
                  <RadioGroup
                    label="Duration Type"
                    orientation="horizontal"
                    classNames={{
                      label: "text-body-bold",
                    }}
                    defaultValue="life-time"
                    value={durationType}
                    onValueChange={(val: string) => {
                      field.onChange(val);
                    }}
                    isInvalid={!!errors?.discount?.durationType}
                  >
                    <Radio
                      classNames={{
                        label: "text-body",
                      }}
                      value="life-time"
                      size="sm"
                      className="mr-3"
                    >
                      Life time
                    </Radio>
                    <Radio
                      classNames={{
                        label: "text-body",
                      }}
                      value="time-base"
                      size="sm"
                    >
                      Time Base
                    </Radio>
                  </RadioGroup>
                </>
              )}
            />
          </div>
        </div>

        <div
          className={`grid ${
            discountType === "promo-code"
              ? " 2xl:grid-cols-2"
              : "2xl:grid-cols-2"
          }   items-start gap-4 mt-8`}
        >
          {/* PROMO CODE */}
          {discountType === "promo-code" && (
            <CustomInput
              label="Code"
              placeholder="Type code"
              isRequireField={true}
              {...register("discount.promoCode")}
              type="text"
              value={promoCode?.toUpperCase()}
              isInvalid={!!errors?.discount?.promoCode}
              errorMessage={errors?.discount?.promoCode?.message}
            />
          )}

          {/* AMOUNT */}
          <CustomNumberInput
            label="Amount"
            {...register("discount.amount")}
            value={watch("discount.amount")}
            isRequireField={true}
            startContent={<BiDollar className="text-gray-400" />}
            isInvalid={!!errors?.discount?.amount}
            errorMessage={errors?.discount?.amount?.message}
          />

          {/* TIME RANGE */}
          <div
            className={`w-full ${
              discountType === "promo-code" ? "2xl:col-span-1" : ""
            }`}
          >
            {durationType === "life-time" ? (
              <Controller
                name={"discount.duration"}
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label="Start Date"
                    aria-label="Select Start date"
                    minValue={today(getLocalTimeZone())}
                    variant="bordered"
                    labelPlacement="outside"
                    value={
                      duration?.start
                        ? parseDate(duration?.start.split("T")[0]) // "2025-09-24"
                        : null
                    }
                    onChange={(value: any) => {
                      if (!value) return;
                      const start =
                        convertToInternationalizedDateTimeToReadble(value);
                      field.onChange({
                        start,
                        end: null,
                      });
                    }}
                    classNames={{
                      label:
                        "after:content-['*'] after:text-red-500 after:ml-1",
                    }}
                    isDisabled={isDiscount}
                    isInvalid={!!errors?.discount?.duration?.start}
                    errorMessage={errors?.discount?.duration?.start?.message}
                  />
                )}
              />
            ) : (
              <Controller
                name={"discount.duration"}
                control={control}
                render={({ field }) => (
                  <DateRangePicker
                    label="Select  Duration"
                    pageBehavior="single"
                    visibleMonths={3}
                    minValue={today(getLocalTimeZone())}
                    variant="bordered"
                    labelPlacement="outside"
                    value={
                      duration?.start && duration?.end
                        ? {
                            start: parseDate(duration?.start.split("T")[0]),
                            end: parseDate(duration?.end.split("T")[0]),
                          }
                        : null
                    }
                    onChange={(value: any) => {
                      if (!value?.start || !value?.end) return;
                      field.onChange({
                        start: convertToInternationalizedDateTimeToReadble(
                          value.start
                        ),
                        end: convertToInternationalizedDateTimeToReadble(
                          value.end
                        ),
                      });
                    }}
                    classNames={{
                      label:
                        "after:content-['*'] after:text-red-500 after:ml-1",
                    }}
                    isInvalid={!!errors?.discount?.duration?.start}
                    errorMessage={errors?.discount?.duration?.start?.message}
                  />
                )}
              />
            )}
          </div>

          {/* MAX COUNT */}
          <CustomNumberInput
            label="Max Count"
            {...register("discount.maxCount")}
            value={watch("discount.maxCount")}
            startContent={<FaUser className="text-gray-400" size={14} />}
            isInvalid={!!errors?.discount?.maxCount}
            errorMessage={errors?.discount?.maxCount?.message}
            description="The maximum number of times this discount can be applied. Once this limit is reached, the discount will automatically expire. To keep it always available, set the value to 0."
          />
        </div>
      </div>
    </>
  );
});
export default DiscountSection;
