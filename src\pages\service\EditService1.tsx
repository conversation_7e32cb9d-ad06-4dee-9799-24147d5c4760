import {
  Controller,
  FormProvider,
  useFieldArray,
  useForm,
} from "react-hook-form";
import { defaultServiceValues } from "../../utils/defaultValue";
import CustomButton from "../../components/ui/CustomButton";
import { BiDollar } from "react-icons/bi";
import {
  addToast,
  Avatar,
  Card,
  CardBody,
  CardHeader,
  DatePicker,
  DateRangePicker,
  Divider,
  Radio,
  RadioGroup,
  RangeValue,
  Select,
  SelectedItems,
  Selection,
  SelectItem,
  Switch,
} from "@heroui/react";
import CustomInput from "../../components/ui/CustomInput";
import {
  useFetchCategory,
  useFetchServiceDataById,
  useFetchStaff,
  useFetchSubCategory,
  useFetchUserDetailsById,
} from "../../hooks/queries/useFetchData";
import CustomAutocomplete from "../../components/ui/CustomAutocomplete";
import {
  availabilityFormatForDisplay,
  createSlug,
  formatCommaSeperateArrayToText,
  formateDataForDropdown,
  formatServiceEditPayload,
  processAdditionalServices,
  processAvailabilityNew,
  processPackage,
} from "../../utils/serviceUtils";
import { useEffect, useState } from "react";
import {
  IAdditionalServicesResponse,
  IFaqProps,
  IGallaryProps,
  IPackageProps,
  IServiceProps,
  IServiceSubmitProps,
  iStaffGetProps,
  ISubcategoryProps,
} from "../../types";
import CustomNumberInput from "../../components/ui/CustomNumberInput";
import CustomChip from "../../components/ui/CustomChip";
import { IoIosAddCircleOutline, IoMdClose } from "react-icons/io";
import { RiDeleteBin4Line } from "react-icons/ri";
import TextEditor from "../../components/ui/TextEdior";
import GallaryInput from "../../components/ui/GallaryInput";
import { BsLink45Deg } from "react-icons/bs";
import CustomCheckbox from "../../components/ui/CustomCheckbox";
import { convertToInternationalizedDateTimeToReadble } from "../../utils/convertTime";
import { yupResolver } from "@hookform/resolvers/yup";
import { serviceSchema } from "../../validation/serviceSchema";
import CustomDivider from "../../components/ui/CustomDivider";
import LocationInputs from "../../components/LocationInputs";
import { useParams } from "react-router-dom";
import FullLoading from "../../components/ui/FullLoading";
import { multipleFileUplaodHelper } from "../../utils/common";
import { useAuth } from "react-oidc-context";
import {
  useUpdateServiceMutation,
  useUpdateServicePackage,
} from "../../hooks/mutations/useUpdateData";
import PackageSection from "../../components/ui/PackageSection";
import { FaUser } from "react-icons/fa";
import {
  CalendarDate,
  getLocalTimeZone,
  parseDate,
  today,
} from "@internationalized/date";
import ServicePreview from "./ServicePreview";
import AvailabilitySection from "../../components/ui/AvailabilitySection";
import SeoSection from "../../components/ui/SeoSection";
import PageHeader from "../../components/ui/PageHeader";
import CustomCardWithHeader from "../../components/ui/CustomCardWithHeader";

interface IDropdownData {
  label: string;
  id: string;
}

const EditService1 = () => {
  const auth = useAuth();
  const { id } = useParams();

  const providerId = auth.user?.profile?.preferred_username || "";
  console.log("Provider ID: ", providerId);

  const [isLoading, setIsLoading] = useState(false);
  const [categoryList, setCategoryList] = useState<IDropdownData[]>([]);
  const [subcategoryList, setSubcategoryList] = useState<IDropdownData[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<Selection>(new Set([]));
  const [isAddtional, setIsAddtional] = useState(false);
  const [isFaq, setIsFaq] = useState(false);
  const [isPackage, setIsPackage] = useState(false);

  //API DATA
  const { data, isFetching } = useFetchServiceDataById(id);
  const { data: apiCategoryData } = useFetchCategory();
  const { data: apiSubCategoryData } = useFetchSubCategory();
  const { data: apiStaffData } = useFetchStaff({ page: 1, limit: 100 });
  const { data: providerDetails } = useFetchUserDetailsById(providerId);
  const { mutateAsync } = useUpdateServiceMutation();
  const { mutateAsync: updateServicePackageMutate } = useUpdateServicePackage();

  console.log("Provider details: ", providerDetails);
  console.log("SERVICE DATA: ", data);

  const form = useForm<IServiceSubmitProps>({
    defaultValues: defaultServiceValues,
    shouldUnregister: true,
    resolver: yupResolver(serviceSchema),
    mode: "onChange",
    reValidateMode: "onChange",
    context: { isFaq, isAddtional, isPackage },
  });

  const {
    register,
    control,
    setValue,
    resetField,
    watch,
    reset,
    trigger,
    getValues,
    clearErrors,
    formState: { errors },
  } = form;

  const isPackageSelect = watch("isPackage");
  const discountType = watch("discount.discountType");
  const discountDurationType = watch("discount.durationType");
  const categoryId = watch("categoryId");
  const fullData = watch();

  console.log("FUll Watch: ", fullData);

  useEffect(() => {
    setIsPackage(isPackageSelect);
  }, [isPackageSelect]);

  //SET API DATA
  useEffect(() => {
    if (isFetching || !data?.serviceInfo) return;

    const apiData: IServiceProps = data.serviceInfo;

    const formatAvailability = availabilityFormatForDisplay(
      apiData.availability
    );

    console.log("API DATA: ", apiData);
    setValue("serviceId", apiData.serviceId || "");
    setValue("serviceTitle", apiData.serviceTitle || "");
    setValue("categoryId", apiData.categoryId || "");
    setValue("subCategoryId", apiData.subCategoryId || "");
    setValue("price", apiData.price || 0);
    setSelectedStaff(new Set(apiData.staff));
    setValue("includes", apiData.includes);
    setValue("additionalServices", apiData.additionalServices);
    setValue("serviceOverview", apiData.serviceOverview);
    setValue("gallery.0", apiData.gallery[0] || "");
    setValue("faq", apiData.faq || []);
    setValue("location.0", apiData.location[0] || []);
    setValue("availability", formatAvailability);
    setValue("isActive", apiData.isActive || false);
    setValue("isfaq", Array.isArray(apiData?.faq) && apiData.faq.length > 0);
    setIsFaq(Array.isArray(apiData?.faq) && apiData.faq.length > 0);

    setValue("seo.0.metaTitle", apiData?.seo?.[0]?.metaTitle || "");
    setValue(
      "seo.0.metaKeywords",
      formatCommaSeperateArrayToText(apiData?.seo?.[0]?.metaKeywords ?? [])
    );
    setValue("seo.0.metaDescription", apiData?.seo?.[0]?.metaDescription || "");

    setValue(
      "isAddtional",
      Array.isArray(apiData?.additionalServices) &&
        apiData.additionalServices.length > 0
    );
    setIsAddtional(
      Array.isArray(apiData?.additionalServices) &&
        apiData.additionalServices.length > 0
    );

    setValue("isPackage", apiData.isPackage || false);
    setValue("isDiscount", apiData.isDiscount || false);

    setValue("packages", apiData.packages || []);

    setValue("discount", apiData.discount || []);
  }, [data?.serviceInfo, setValue, isFetching]);

  // FORMAT PKG FOR DISPLAY
  const normalizePackageData = (pkg: any) => ({
    ...pkg,
    includes: Array.isArray(pkg?.includes)
      ? pkg.includes.map((inc: string) => ({ value: inc }))
      : [],
  });

  // useEffect(() => {
  //   if (data?.serviceInfo) {
  //     if (!data?.serviceInfo?.packages) return;
  //     const normalized = data?.serviceInfo?.packages.map(normalizePackageData);
  //     reset({ packages: normalized });
  //   }
  // }, [data?.serviceInfo, reset]);

  useEffect(() => {
    if (data?.serviceInfo?.packages) {
      const normalized = data.serviceInfo.packages.map(normalizePackageData);
      reset({
        ...getValues(), // keep current form values
        packages: normalized, // override only packages
      });
    }
  }, [data?.serviceInfo, reset, getValues]);

  //Category formating for display value in dropdown
  useEffect(() => {
    const formtedCategory = formateDataForDropdown(
      apiCategoryData?.categories,
      "categoryName",
      "categoryId"
    );
    if (!formtedCategory) return;

    setCategoryList(formtedCategory);
  }, [apiCategoryData?.categories]);

  //SubCategory formating for display value in dropdown
  useEffect(() => {
    const getSubcategorybyCategoryId = apiSubCategoryData?.subCategories.filter(
      (item: ISubcategoryProps) => {
        return item.categoryId === categoryId;
      }
    );
    if (!getSubcategorybyCategoryId) return;

    const formtedSubCategory = formateDataForDropdown(
      getSubcategorybyCategoryId,
      "subCategoryName",
      "subCategoryId"
    );

    setSubcategoryList(formtedSubCategory);
  }, [categoryId, apiSubCategoryData?.subCategories]);

  const { fields, append, remove } = useFieldArray({
    name: "includes",
    control,
  });

  useEffect(() => {
    if (fields.length === 0) append("");
    if (addtionalServiceField.length === 0)
      appendAddtionalService({
        serviceItem: "",
        price: 0,
        id: "",
        images: "",
      });
  }, [append, fields.length]);

  const {
    fields: addtionalServiceField,
    append: appendAddtionalService,
    remove: removeAdditionalService,
  } = useFieldArray({
    name: "additionalServices",
    control,
  });

  const {
    fields: faqField,
    append: appendFaq,
    remove: removeFaq,
  } = useFieldArray({
    name: "faq",
    control,
  });

  useEffect(() => {
    setValue("isfaq", isFaq);
    if (!isFaq) {
      resetField("faq");
    }
  }, [isFaq, resetField, setValue]);

  useEffect(() => {
    setValue("isAddtional", isAddtional);
    if (!isAddtional) {
      // resetField("additionalServices.0");
      // resetField("additionalServices.0.id");
      // resetField("additionalServices.0.images");
      // resetField("additionalServices.0.price");
      // resetField("additionalServices.0.serviceItem");
      setValue("additionalServices", [
        {
          serviceItem: "",
          price: 0,
          id: "",
          images: "",
        },
      ]);
    }
  }, [isAddtional, resetField, setValue]);

  //SUBMIT DATA
  const onSubmit = async (data: IServiceSubmitProps) => {
    console.log("RUNNN.....................OnSubmit");
    setIsLoading(true);
    try {
      let staffArray = [];
      let addtionalServiceResult: IAdditionalServicesResponse[] = [];
      let formatFaqs: IFaqProps[] | [] = [];
      let galleryImageUrls: string[] = [];
      let processPackageData: IPackageProps[] = [];

      const files = data.gallery?.[0]?.serviceImages;
      const additionalServices = data?.additionalServices;
      const user = providerDetails?.user;
      console.log("User: ", user);
      const nestedFilePath = `${data?.serviceTitle}-${Date.now()}`;
      const generateProviderFolder = `${user.userId}-${user.name}`;
      const availability = data?.availability;
      const serviceId = data?.serviceId;
      const selectFaq = data?.faq;
      const selectIncludes = data?.includes;
      const isPackages = data?.isPackage;
      const packageData = data?.packages;

      console.log("Step - 1");
      // //Image upload
      if (!user || !data?.serviceTitle || !generateProviderFolder) {
        addToast({
          title: "Configuration Error",
          description:
            "Upload configuration parameters are missing. Please contact support if the issue persists.",
          radius: "md",
          color: "danger",
        });
        throw new Error("Upload configuration parameters are missing");
      }
      console.log("Step - 2");
      // Get existing URLs
      const existingUrls = (
        Array.isArray(files)
          ? files.filter((item) => typeof item === "string")
          : []
      ) as string[];

      console.log("Step - 3");
      if (Array.isArray(files)) {
        const realFiles = files.filter(
          (item: File) => item instanceof File
        ) as File[];

        if (realFiles.length > 0) {
          galleryImageUrls = await multipleFileUplaodHelper({
            files: realFiles,
            baseFolder: "provider",
            mainFolder: "service",
            subFolder: generateProviderFolder,
            nestedPath: nestedFilePath,
            errorMessageType: "Image",
          });
        } else {
          galleryImageUrls = [];
        }
      }
      //combine old urls with new urls
      const allGalleryUrls = [...existingUrls, ...galleryImageUrls];
      console.log("Step - 4");
      if (!allGalleryUrls || allGalleryUrls.length === 0) {
        addToast({
          title: "Image upload",
          description:
            "Uplaod failed. Please contact support if the issue persists.",
          radius: "md",
          color: "danger",
        });
        throw new Error("Image upload failed");
      }
      console.log("Step - 5");
      //ADDTIONAL SERVIVE IMAGE UPLOAD
      if (additionalServices?.[0].images && additionalServices?.length != 0) {
        addtionalServiceResult = await processAdditionalServices(
          additionalServices,
          generateProviderFolder,
          nestedFilePath
        );
      } else {
        addtionalServiceResult = [];
      }
      console.log("Step - 6");
      if (
        Array.isArray(additionalServices) &&
        additionalServices.length > 0 &&
        additionalServices?.[0]?.images?.length > 0
      ) {
        addtionalServiceResult = await processAdditionalServices(
          additionalServices,
          generateProviderFolder,
          nestedFilePath
        );
      } else {
        addtionalServiceResult = [];
      }

      if (isPackages) {
        processPackageData = await processPackage(packageData);
        console.log("EDIT FORMAT PKG: ", processPackageData);
      }

      console.log("Step - 7");
      const generatSlug = await createSlug(data?.serviceTitle);
      if (!generatSlug) {
        addToast({
          title: "Slug generation",
          description: "Slug generation failed. try again.",
          radius: "md",
          color: "danger",
        });
        throw new Error("Slug generation failed. try again.");
      }

      // Process staff selection
      staffArray = Array.from(selectedStaff);
      console.log("Step - 8");
      if (staffArray.length === 0) {
        staffArray.push(user.userId);
      }
      console.log("Step - 9");
      const processAvilabilityResult = await processAvailabilityNew(
        availability
      );
      console.log("Step - 10");
      if (selectFaq && selectFaq.length > 0) {
        if (
          (selectFaq[0]?.answer ?? "").trim() === "" &&
          (selectFaq[0]?.question ?? "").trim() === ""
        ) {
          formatFaqs = [];
        } else {
          formatFaqs = selectFaq;
        }
      }
      console.log("Step - 11");
      // const formatIncludes = selectIncludes?.filter(
      //   (item) => item.trim() !== ""
      // );
      const formatIncludes = Array.isArray(selectIncludes)
        ? selectIncludes.filter((item) => item && item.trim() !== "")
        : [];

      const payload = {
        slug: generatSlug,
        price: Number(data.price),
        staff: staffArray,
        additionalService: addtionalServiceResult,
        gallery: [
          {
            serviceImages: allGalleryUrls,
            videoLink: data?.gallery[0]?.videoLink,
          },
        ],
        availability: processAvilabilityResult,
        faq: formatFaqs,
        includes: formatIncludes,
        packages: processPackageData,
      };

      console.log("Final Service update payload: ", payload);

      const formatEditPayload = await formatServiceEditPayload(data, payload);

      console.log("EDIT FORMAT DATA : ", formatEditPayload);
      console.log("EDIT FORMAT processPackageData : ", processPackageData);
      console.log("EDIT FORMAT packageData : ", packageData);

      const finalPayload = {
        ...formatEditPayload,
        packages: processPackageData,
      };

      console.log("Final Paylaod: ", finalPayload);

      await mutateAsync({ id: serviceId, serviceData: finalPayload });
      reset();
      clearErrors();
    } catch (e: any) {
      console.log("ERROR EDIT SERVICE: ", e);
      addToast({
        title: "Update Error",
        description: e.toString() || "Service update failed. Try again.",
        radius: "md",
        color: "danger",
      });
    } finally {
      setIsLoading(false);
    }
  };

  console.log("ERROR: ", errors);
  console.log("EDIT FORMAT watch : ", watch());

  return (
    <>
      {isFetching && <FullLoading label="Loading..." />}
      {isLoading && (
        <FullLoading label="Please wait, this may take some time. Submitting..." />
      )}

      <PageHeader
        title="Edit Service"
        description="Edit your service details and update your portfolio."
        components={
          <div className="flex gap-5">
            <div className="flex flex-initial justify-end items-center">
              <ServicePreview data={fullData} category={apiCategoryData} />
            </div>
          </div>
        }
      />
      {/* <div className="flex justify-end  mb-3">
        <ServicePreview data={fullData} category={apiCategoryData} />
      </div> */}
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <CustomCardWithHeader
                title="Basic Information"
                subtitle="Fill in the basic information of your service"
                isHeaderDevider
                className="mr-5"
                mainContent={
                  <div>
                    <div className="gap-3 tablet:gap-3 -mt-2 ">
                      <CustomInput
                        label="Service Title"
                        type="text"
                        placeholder="Enter title"
                        value={watch("serviceTitle")}
                        isInvalid={!!errors?.serviceTitle}
                        errorMessage={errors?.serviceTitle?.message}
                        {...register("serviceTitle")}
                      />

                      {/* Slug */}
                      {/* <CustomInput
                    label="Service Slug"
                    type="text"
                    isRequired
                    placeholder="Enter slug"
                    // value={generateSlug()}
                    isInvalid={!!errors?.slug}
                    errorMessage={errors?.slug?.message}
                    {...register("slug")}
                  /> */}
                      <div className="grid tablet:grid-cols-2 gap-4 tablet:gap-4  mt-4">
                        <div>
                          <Controller
                            name="categoryId"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <>
                                <CustomAutocomplete
                                  label="Category"
                                  placeholder="Select category"
                                  selectedKey={watch("categoryId")}
                                  defaultItems={categoryList}
                                  width="none"
                                  onSelectionChange={(val) => {
                                    field.onChange(val);
                                  }}
                                  isInvalid={!!errors.categoryId?.message}
                                  errorMessage={errors.categoryId?.message}
                                />
                              </>
                            )}
                          />
                        </div>
                        <div>
                          <Controller
                            name="subCategoryId"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <>
                                <CustomAutocomplete
                                  label="Sub Category"
                                  placeholder="Select sub category"
                                  selectedKey={watch("subCategoryId")}
                                  defaultItems={subcategoryList}
                                  width="none"
                                  onSelectionChange={(val) => {
                                    field.onChange(val);
                                  }}
                                  description="First select a category"
                                  isInvalid={!!errors.subCategoryId?.message}
                                  errorMessage={errors.subCategoryId?.message}
                                />
                              </>
                            )}
                          />
                        </div>
                      </div>

                      {/* STAFF */}
                      <Controller
                        name="staff"
                        defaultValue={[]}
                        control={control}
                        render={({ field, fieldState }) => (
                          <Select
                            {...field}
                            variant="bordered"
                            label="Add Staff"
                            labelPlacement="outside"
                            placeholder="Select a service"
                            selectionMode="multiple"
                            selectedKeys={selectedStaff}
                            onSelectionChange={setSelectedStaff}
                            items={apiStaffData?.staff || []}
                            errorMessage={fieldState.error?.message}
                            renderValue={(
                              items: SelectedItems<iStaffGetProps>
                            ) => (
                              <div className="flex flex-wrap gap-2">
                                {items.map((item) => (
                                  <CustomChip
                                    key={item.key}
                                    label={item?.data?.fullName}
                                  />
                                ))}
                              </div>
                            )}
                          >
                            {(data: iStaffGetProps) => (
                              <SelectItem
                                key={data.staffId}
                                textValue={data.fullName}
                              >
                                <div className="flex gap-2 items-center">
                                  <Avatar
                                    alt={data.fullName}
                                    className="shrink-0"
                                    size="sm"
                                  />
                                  <div className="flex flex-col">
                                    <span className="text-small">
                                      {data.fullName}
                                    </span>
                                    <span className="text-tiny text-default-400">
                                      {data.staffId}
                                    </span>
                                  </div>
                                </div>
                              </SelectItem>
                            )}
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                }
              />

              {/* PRICE AND PACKAGE*/}
              <CustomCardWithHeader
                title="Price & Package"
                subtitle="Add your service price and package"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <>
                    <PackageSection />
                  </>
                }
              />

              {!isPackageSelect && (
                <CustomCardWithHeader
                  title="Discount"
                  subtitle="Add your service discount"
                  isHeaderDevider
                  className="mt-5 mr-5"
                  mainContent={
                    <>
                      <div className="-mt-2">
                        <div className="flex items-baseline flex-initial gap-4 ">
                          <p className="text-body-bold ">Enable Discount</p>
                          <Controller
                            name="discount.isDiscount"
                            control={control}
                            defaultValue={false}
                            render={({ field }) => (
                              <Switch
                                isSelected={watch("discount.isDiscount")}
                                onValueChange={(val: boolean) => {
                                  field.onChange(val);
                                  setValue("isDiscount", val);
                                  if (!val) {
                                    resetField("discount");
                                  }
                                  trigger("discount.isDiscount");
                                }}
                                size="sm"
                                className="mb-2"
                              />
                            )}
                          />
                        </div>
                        <div className="grid tablet:grid-cols-2 laptop:grid-cols-3 gap-3">
                          <div className="flex flex-col ">
                            {/* DISCOUNT DURATION TYPE */}
                            <div
                              className={`${
                                errors.discount?.discountType
                                  ? "border-red-500 bg-red-50 dark:border-red-500"
                                  : ""
                              } border-1  border-secondary/30 rounded-lg p-3 dark:border-gray-600/50  bg-cyan-100 dark:bg-neutral-800`}
                            >
                              <Controller
                                name="discount.discountType"
                                control={control}
                                defaultValue="general-discount"
                                render={({ field }) => (
                                  <>
                                    <RadioGroup
                                      label="Discount Type"
                                      orientation="horizontal"
                                      classNames={{
                                        label: "text-body1",
                                      }}
                                      defaultValue="general-discount"
                                      value={watch("discount.discountType")}
                                      onValueChange={(val: string) => {
                                        field.onChange(val);
                                        if (val === "promo-code") {
                                          resetField("discount.promoCode");
                                        }
                                      }}
                                      isInvalid={
                                        !!errors.discount?.discountType
                                      }
                                      isDisabled={!watch("discount.isDiscount")}
                                    >
                                      <Radio
                                        classNames={{
                                          label: "text-sm dark:text-gray-300",
                                        }}
                                        value="general-discount"
                                        size="sm"
                                        className="mr-3"
                                      >
                                        General Discount
                                      </Radio>
                                      <Radio
                                        classNames={{
                                          label: "text-sm dark:text-gray-300",
                                        }}
                                        value="promo-code"
                                        size="sm"
                                      >
                                        Promo Code
                                      </Radio>
                                    </RadioGroup>
                                  </>
                                )}
                              />
                            </div>
                            {errors.discount?.discountType && (
                              <span className="text-error">
                                {errors.discount?.discountType.message}
                              </span>
                            )}
                          </div>

                          {/* DISCOUNT TYPE */}
                          <div className=" border-1 border-secondary/30 rounded-lg p-3 dark:border-gray-600/50 bg-cyan-100 dark:bg-neutral-800">
                            <Controller
                              name="discount.durationType"
                              control={control}
                              defaultValue="life-time"
                              render={({ field }) => (
                                <>
                                  <RadioGroup
                                    label="Duration Type"
                                    orientation="horizontal"
                                    classNames={{
                                      label: "text-body1",
                                    }}
                                    value={watch("discount.durationType")}
                                    onValueChange={(val: string) => {
                                      resetField("discount.duration.start");
                                      resetField("discount.duration.end");
                                      field.onChange(val);
                                    }}
                                    isInvalid={!!errors.discount?.durationType}
                                    isDisabled={!watch("discount.isDiscount")}
                                  >
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="life-time"
                                      size="sm"
                                      className="mr-3"
                                    >
                                      Life time
                                    </Radio>
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="time-base"
                                      size="sm"
                                    >
                                      Time Base
                                    </Radio>
                                  </RadioGroup>
                                </>
                              )}
                            />
                            {errors.discount?.valueType && (
                              <span className="text-error">
                                {errors.discount?.valueType?.message}
                              </span>
                            )}
                          </div>

                          {/* DISCOUNT DURATION TYPE */}
                          <div className=" border-1 border-secondary/30 rounded-lg p-3 dark:border-gray-600/50 bg-cyan-100  dark:bg-neutral-800">
                            <Controller
                              name="discount.durationType"
                              control={control}
                              defaultValue="life-time"
                              render={({ field }) => (
                                <>
                                  <RadioGroup
                                    label="Duration Type"
                                    orientation="horizontal"
                                    classNames={{
                                      label: "text-body",
                                    }}
                                    value={watch("discount.durationType")}
                                    onValueChange={(val: string) => {
                                      field.onChange(val);
                                    }}
                                    isInvalid={!!errors.discount?.durationType}
                                    isDisabled={!watch("discount.isDiscount")}
                                  >
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="life-time"
                                      size="sm"
                                      className="mr-3"
                                    >
                                      Life time
                                    </Radio>
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="time-base"
                                      size="sm"
                                    >
                                      Time Base
                                    </Radio>
                                  </RadioGroup>
                                </>
                              )}
                            />
                          </div>
                        </div>

                        <div
                          className={`grid ${
                            discountType === "promo-code"
                              ? " 2xl:grid-cols-3"
                              : "2xl:grid-cols-2"
                          }  items-start gap-4 mt-8`}
                        >
                          {/* AMOUNT */}
                          {discountType === "promo-code" && (
                            <CustomInput
                              label="Code"
                              placeholder="Type code"
                              isRequireField={true}
                              {...register("discount.promoCode")}
                              type="text"
                              value={watch("discount.promoCode")?.toUpperCase()}
                              isInvalid={!!errors?.discount?.promoCode}
                              errorMessage={
                                errors?.discount?.promoCode?.message
                              }
                              isDisabled={!watch("discount.isDiscount")}
                            />
                          )}

                          {/* AMOUNT */}
                          <div className="">
                            <CustomNumberInput
                              label="Amount"
                              placeholder="Enter amount"
                              {...register("discount.amount")}
                              isRequireField={true}
                              value={watch("discount.amount")}
                              startContent={
                                <BiDollar className="text-gray-400" />
                              }
                              isInvalid={!!errors.discount?.amount?.message}
                              errorMessage={errors.discount?.amount?.message}
                              isDisabled={!watch("discount.isDiscount")}
                            />
                          </div>

                          {/* TIME RANGE */}
                          <div>
                            {discountDurationType === "life-time" ? (
                              <Controller
                                name={"discount.duration"}
                                control={control}
                                render={({ field }) => (
                                  <DatePicker
                                    label="Start Date"
                                    aria-label="Select Start date"
                                    minValue={today(getLocalTimeZone())}
                                    variant="bordered"
                                    labelPlacement="outside"
                                    value={
                                      field.value?.start
                                        ? parseDate(
                                            field.value.start.split("T")[0]
                                          ) // "2025-09-24"
                                        : null
                                    }
                                    // onChange={(value: any) => {
                                    //   console.log(value);
                                    //   if (!value) return;
                                    //   field.onChange(
                                    //     convertToInternationalizedDateTimeToReadble(
                                    //       value
                                    //     )
                                    //   );
                                    // }}

                                    onChange={(value) => {
                                      if (!value) return;
                                      const start =
                                        convertToInternationalizedDateTimeToReadble(
                                          value
                                        );
                                      field.onChange({
                                        start,
                                        end: null,
                                      });
                                      // setValue(
                                      //   `packages.${index}.discount.duration.start`,
                                      //   value
                                      // );
                                    }}
                                    classNames={{
                                      label:
                                        "after:content-['*'] after:text-red-500 after:ml-1",
                                    }}
                                    isDisabled={!watch("discount.isDiscount")}
                                    isInvalid={
                                      !!errors?.discount?.duration?.start
                                    }
                                    errorMessage={
                                      errors?.discount?.duration?.start?.message
                                    }
                                  />
                                )}
                              />
                            ) : (
                              <Controller
                                name={"discount.duration"}
                                control={control}
                                render={({ field }) => (
                                  <DateRangePicker
                                    label="Select  Duration"
                                    pageBehavior="single"
                                    visibleMonths={3}
                                    minValue={today(getLocalTimeZone())}
                                    variant="bordered"
                                    labelPlacement="outside"
                                    value={
                                      field.value?.start && field.value?.end
                                        ? {
                                            start: parseDate(
                                              field.value.start.split("T")[0]
                                            ),
                                            end: parseDate(
                                              field.value.end.split("T")[0]
                                            ),
                                          }
                                        : null
                                    }
                                    // onChange={(
                                    //   value: any
                                    // ) => {
                                    //   if (!value?.start || !value?.end) return;
                                    //   field.onChange({
                                    //     start:
                                    //       convertToInternationalizedDateTimeToReadble(
                                    //         value.start
                                    //       ),
                                    //     end: convertToInternationalizedDateTimeToReadble(
                                    //       value.end
                                    //     ),
                                    //   });
                                    // }}
                                    onChange={(value: any) => {
                                      if (!value?.start || !value?.end) return;
                                      field.onChange({
                                        start:
                                          convertToInternationalizedDateTimeToReadble(
                                            value.start
                                          ),
                                        end: convertToInternationalizedDateTimeToReadble(
                                          value.end
                                        ),
                                      });
                                    }}
                                    classNames={{
                                      label:
                                        "after:content-['*'] after:text-red-500 after:ml-1",
                                    }}
                                    isDisabled={!watch("discount.isDiscount")}
                                    isInvalid={
                                      !!errors?.discount?.duration?.start
                                    }
                                    errorMessage={
                                      errors?.discount?.duration?.start?.message
                                    }
                                  />
                                )}
                              />
                            )}
                          </div>

                          {/* MAX COUNT */}
                          <CustomNumberInput
                            label="Usage Limit/Maximum Discount Uses"
                            description="The maximum number of times this discount can be applied. Once this limit is reached, the discount will automatically expire. To keep it always available, set the value to 0."
                            placeholder="Enter count"
                            {...register("discount.maxCount")}
                            value={watch("discount.maxCount")}
                            startContent={
                              <FaUser className="text-gray-400" size={14} />
                            }
                            isInvalid={!!errors.discount?.maxCount?.message}
                            errorMessage={errors.discount?.maxCount?.message}
                            isDisabled={!watch("discount.isDiscount")}
                          />
                        </div>
                      </div>
                    </>
                  }
                />
              )}

              {/* INCLUDE */}
              <CustomCardWithHeader
                title="Service Includes"
                subtitle="Add your what are the service includes"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <div>
                      {fields.map((field, index) => (
                        <div
                          key={field.id}
                          className="flex gap-5 flex-inline justify-center items-center mb-5"
                        >
                          <CustomInput
                            type="text"
                            placeholder="Enter include title"
                            {...register(`includes.${index}`)}
                            isInvalid={!!errors?.includes?.[index]?.message}
                            errorMessage={errors?.includes?.[index]?.message}
                          />

                          {fields.length > 1 && (
                            <div className="mt-6 cursor-pointer">
                              <CustomButton
                                isIconOnly={true}
                                variant="light"
                                onPress={() => remove(index)}
                              >
                                <RiDeleteBin4Line
                                  size={20}
                                  className="text-red-400"
                                />
                              </CustomButton>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    <div className="mt-5 ">
                      <CustomButton
                        label="Add New"
                        className="text-green-600"
                        startContent={
                          <IoIosAddCircleOutline
                            size={20}
                            className="text-green-600 cursor-pointer"
                          />
                        }
                        variant="light"
                        onPress={() => {
                          append();
                        }}
                      />
                    </div>
                  </div>
                }
              />

              <CustomCardWithHeader
                title="Addtional Service"
                subtitle="Add your additional service"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <div className="flex items-beaseline flex-initial gap-4 mb-3">
                      <p className="text-body-bold">
                        Add Addtional Service to Service
                      </p>
                      <Switch
                        isSelected={isAddtional}
                        onValueChange={setIsAddtional}
                        size="sm"
                        className="mb-2"
                      ></Switch>
                    </div>

                    <>
                      {addtionalServiceField.map((field, index) => (
                        <div
                          key={field.id}
                          className="flex gap-5 flex-inline justify-center items-center my-4"
                        >
                          <div className="relative">
                            <label
                              htmlFor={`images-upload-${index}`}
                              className={`rounded-lg flex justify-center items-center cursor-pointer aspect-square w-[60px] h-[60px] 
    ${
      errors?.additionalServices?.[index]?.images?.message
        ? "border border-red-500 bg-red-100"
        : "bg-gray-200 dark:bg-gray-800"
    }`}
                            >
                              {(() => {
                                const imageData = watch(
                                  `additionalServices.${index}.images`
                                );

                                if (
                                  imageData instanceof FileList &&
                                  imageData[0]
                                ) {
                                  //Preview newly selected image
                                  return (
                                    <>
                                      <img
                                        src={URL.createObjectURL(imageData[0])}
                                        alt="Preview"
                                        className="rounded-lg object-contain aspect-square w-full h-full relative"
                                      />
                                      <span
                                        className="absolute -top-2 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex justify-center items-center cursor-pointer"
                                        onClick={() =>
                                          setValue(
                                            `additionalServices.${index}.images`,
                                            null
                                          )
                                        }
                                      >
                                        <IoMdClose />
                                      </span>
                                    </>
                                  );
                                } else if (
                                  typeof imageData === "string" &&
                                  imageData !== ""
                                ) {
                                  // Show existing image URL (e.g., when editing existing service)
                                  return (
                                    <img
                                      src={imageData}
                                      alt="Preview"
                                      className="rounded-lg object-contain aspect-square w-full h-full relative"
                                    />
                                  );
                                } else {
                                  // No image at all yet
                                  return (
                                    <span className="text-gray-500">+</span>
                                  );
                                }
                              })()}
                            </label>
                          </div>

                          <input
                            id={`images-upload-${index}`}
                            type="file"
                            accept="image/*"
                            multiple={false}
                            className="hidden"
                            disabled={!isAddtional}
                            {...register(
                              `additionalServices.${index}.images` as const,
                              {
                                ...(isAddtional && {
                                  required: "Image is required",
                                }),
                              }
                            )}
                          />

                          {/* Name Input */}
                          <CustomInput
                            label="Name"
                            type="text"
                            placeholder="Enter title"
                            isDisabled={!isAddtional}
                            {...register(
                              `additionalServices.${index}.serviceItem` as keyof IServiceProps
                            )}
                            isInvalid={
                              !!errors?.additionalServices?.[index]?.images
                                ?.message
                            }
                            errorMessage={
                              errors?.additionalServices?.[index]?.images
                                ?.message
                            }
                          />

                          {/* Price Input */}
                          <CustomNumberInput
                            label="Price"
                            placeholder="Enter Price"
                            isDisabled={!isAddtional}
                            startContent={
                              <BiDollar className="text-gray-400" />
                            }
                            {...register(
                              `additionalServices.${index}.price` as keyof IServiceProps
                            )}
                            isInvalid={
                              !!errors?.additionalServices?.[index]?.price
                                ?.message
                            }
                            errorMessage={
                              errors?.additionalServices?.[index]?.price
                                ?.message
                            }
                          />

                          {/* Remove Button */}
                          {addtionalServiceField.length > 1 && (
                            <div className="mt-6 cursor-pointer">
                              <CustomButton
                                isIconOnly={true}
                                variant="light"
                                onPress={() => removeAdditionalService(index)}
                              >
                                <RiDeleteBin4Line
                                  size={20}
                                  className="text-red-400"
                                />
                              </CustomButton>
                            </div>
                          )}
                        </div>
                      ))}

                      {/* Add New Button */}
                      <div className="mt-3">
                        <CustomButton
                          label="Add New"
                          className="text-green-600"
                          isDisabled={!isAddtional}
                          startContent={
                            <IoIosAddCircleOutline
                              size={20}
                              className="text-green-600 cursor-pointer"
                            />
                          }
                          variant="light"
                          onPress={() => {
                            appendAddtionalService({
                              id: "",
                              images: "",
                              serviceItem: "",
                              price: 0,
                            });
                          }}
                        />
                      </div>
                    </>
                  </div>
                }
              />

              {/* SERVICE ORVERVIEW */}
              <CustomCardWithHeader
                title="Service Overview"
                subtitle="Add service overview"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <Controller
                      name="serviceOverview"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <>
                          <TextEditor
                            value={field.value}
                            onChangeValue={(value) => {
                              field.onChange(value);
                            }}
                            isError={!!errors?.serviceOverview?.message}
                          />
                          {errors?.serviceOverview?.message && (
                            <p className="text-error mt-5">
                              {errors?.serviceOverview?.message}
                            </p>
                          )}
                        </>
                      )}
                    />
                  </div>
                }
              />

              <CustomCardWithHeader
                title="Gallary"
                subtitle="Add service gallary"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <div className="gap-6 mb-5">
                      <Controller
                        name="gallery.0.serviceImages"
                        control={control}
                        defaultValue={[]} // not an object — this should match yup's expected type
                        render={({ field }) => (
                          <GallaryInput
                            value={field.value} // just an array of string | File
                            onChangeValue={(newVal) =>
                              field.onChange(newVal.images)
                            } // extract array only
                            error={errors.gallery?.[0]?.serviceImages?.message}
                          />
                        )}
                      />
                    </div>
                    {errors?.gallery?.[0]?.serviceImages?.message && (
                      <small className="text-danger ">
                        {errors.gallery?.[0]?.serviceImages?.message}
                      </small>
                    )}

                    <div className="mt-10">
                      <CustomInput
                        size="md"
                        label="Video Link"
                        description="Only YouTube link are supported."
                        type="text"
                        placeholder="https://www.example.com"
                        value={watch("gallery.0.videoLink")}
                        {...register("gallery.0.videoLink")}
                        startContent={<BsLink45Deg />}
                        isInvalid={!!errors?.gallery?.[0]?.videoLink}
                        errorMessage={errors?.gallery?.[0]?.videoLink?.message}
                      />
                    </div>
                  </div>
                }
              />
            </div>

            {/* RIGHT SIDE */}
            <div>
              <CustomCardWithHeader
                title="Availability"
                subtitle="Set the availability of your service"
                isHeaderDevider
                mainContent={
                  <div className="-mt-2">
                    <AvailabilitySection />
                  </div>
                }
              />

              {/* LOCATION */}
              <CustomCardWithHeader
                title="Service Location"
                subtitle="Add the service location"
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <LocationInputs />
                  </div>
                }
              />

              {/* FAQ */}
              <CustomCardWithHeader
                title="FAQ"
                subtitle="Add frequently asked questions about your service"
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <div>
                      <div className="flex items-baseline flex-initial gap-4">
                        <p className="text-md font-medium">Enable FAQ</p>
                        <Switch
                          isSelected={isFaq}
                          onValueChange={setIsFaq}
                          size="sm"
                          className="mb-2"
                        ></Switch>
                      </div>
                    </div>

                    <div className="gap-6 ">
                      {faqField.map((field, index) => (
                        <div
                          key={field.id}
                          className="flex gap-5 flex-inline justify-center items-center"
                        >
                          <CustomInput
                            label="Question"
                            type="text"
                            placeholder="Enter your question"
                            isDisabled={!isFaq}
                            {...register(`faq.${index}.question` as const)}
                            isInvalid={!!errors.faq?.[index]?.question?.message}
                            errorMessage={
                              errors.faq?.[index]?.question?.message
                            }
                          />

                          <CustomInput
                            label="Answer"
                            type="text"
                            placeholder="Enter your answer"
                            isDisabled={!isFaq}
                            {...register(`faq.${index}.answer` as const)}
                            isInvalid={!!errors.faq?.[index]?.answer?.message}
                            errorMessage={errors.faq?.[index]?.answer?.message}
                          />

                          {faqField.length > 1 && (
                            <div className="mt-6 cursor-pointer">
                              <CustomButton
                                isIconOnly={true}
                                isDisabled={!isFaq}
                                variant="light"
                                onPress={() => removeFaq(index)}
                              >
                                <RiDeleteBin4Line
                                  size={20}
                                  className="text-red-400"
                                />
                              </CustomButton>
                            </div>
                          )}
                        </div>
                      ))}

                      <div className="mt-5">
                        <CustomButton
                          label="Add FAQ"
                          color="success"
                          isDisabled={!isFaq}
                          startContent={
                            <IoIosAddCircleOutline
                              size={20}
                              className="text-green-600 cursor-pointer"
                            />
                          }
                          variant="flat"
                          onPress={() =>
                            appendFaq({
                              question: "",
                              answer: "",
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>
                }
              />

              {/* SEO */}
              <CustomCardWithHeader
                title="SEO"
                subtitle="Add SEO details for your service"
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <SeoSection />
                  </div>
                }
              />

              {/* Active Service */}
              <CustomCardWithHeader
                title="Service Status"
                subtitle="Control whether this service is visible to customers."
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <Controller
                      name={"isActive"}
                      control={control}
                      defaultValue={true}
                      render={({ field }) => (
                        <CustomCheckbox
                          label="Is Service Active?"
                          isSelected={field.value}
                          onValueChange={field.onChange}
                        />
                      )}
                    />
                  </div>
                }
              />
            </div>
          </div>

          <div className="flex flex-initial justify-end items-end my-1 gap-5">
            <CustomButton
              label="Clear"
              type="reset"
              color="danger"
              size="md"
              variant="flat"
            />
            <CustomButton
              label="Sumbit"
              type="submit"
              color="primary"
              size="md"
              // isDisabled={!isVerifyAccount ? true : false}
              isDisabled={false}
            />
          </div>
        </form>
      </FormProvider>
    </>
  );
};

export default EditService1;
