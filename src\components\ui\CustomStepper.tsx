import { FaArrow<PERSON>eftLong, FaCircleCheck } from "react-icons/fa6";
import {
  convertDateToReadble,
  convertTimeTo12ClockWithAmPm,
} from "../../utils/convertTime";
import { BookingStatus } from "../../types";
import { MdArrowForwardIos } from "react-icons/md";

interface AuditLog {
  action: string;
  performedBy: string;
  timestamp: string;
}

interface CustomStepperProps {
  status: "Pending" | "Confirmed" | "InProgress" | "Completed" | "Cancelled";
  auditLogs: AuditLog[];
}

const stepMap: Record<string, number> = {
  Pending: 1,
  Confirmed: 2,
  InProgress: 3,
  Completed: 4,
  Cancelled: 0,
};

const findAuditForStep = (step: string, auditLogs: AuditLog[]) => {
  if (step === BookingStatus.PENDING) {
    return auditLogs.find((log) =>
      log.action.toLowerCase().includes("created booking")
    );
  }
  return auditLogs.find((log) =>
    log.action.toLowerCase().includes(step.toLowerCase())
  );
};

const steps = [
  BookingStatus.PENDING,
  BookingStatus.CONFIRMED,
  BookingStatus.INPROGRESS,
  BookingStatus.COMPLETED,
];

const CustomStepper = ({ status, auditLogs }: CustomStepperProps) => {
  const currentStep = stepMap[status];

  return (
    <ol
      className={` items-start w-full space-y-4 sm:flex sm:space-x-8 sm:space-y-0 rtl:space-x-reverse$ ${
        status == BookingStatus.COMPLETED ? "" : "md:gap-10"
      }`}
    >
      {steps.map((step, index) => {
        const stepNumber = stepMap[step];
        const isCompleted = currentStep >= stepNumber;
        const isInProgress = 3 == currentStep;
        const audit = findAuditForStep(step, auditLogs);
        const isLast = index === steps.length - 1;
        return (
          <li
            key={index}
            className="flex items-start  space-x-2.5 rtl:space-x-reverse"
          >
            <FaCircleCheck
              size={22}
              className={`flex items-center justify-center w-[26px] h-[26px]  rounded-full ${
                isCompleted ? " text-green-500" : " text-gray-300"
              }`}
            />
            <span>
              <h3
                className={`text-body-bold ${
                  isCompleted ? "text-green-500" : "text-gray-300"
                }`}
              >
                {step}
              </h3>
              <p
                className={`text-caption text-center ${
                  isCompleted ? "text-gray-500" : "text-gray-300"
                }`}
              >
                {audit
                  ? `${convertDateToReadble(audit.timestamp)}
                     at ${convertTimeTo12ClockWithAmPm(audit.timestamp)}`
                  : "--"}
              </p>
              {step === "InProgress" && isInProgress && (
                <p className="text-xs text-gray-500">(Performed by system)</p>
              )}
            </span>

            {!isLast && (
              <div className="flex">
                <FaArrowLeftLong className="rotate-180 ml-5 text-gray-500" />
              </div>
            )}
          </li>
        );
      })}
    </ol>
  );
};

export default CustomStepper;
