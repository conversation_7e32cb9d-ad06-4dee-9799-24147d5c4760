# gigmosaic-provider-dashboard: Copilot Instructions

## Project Overview

- **React + TypeScript** dashboard built with **Vite** and **Yarn**.
- Uses **TailwindCSS** for styling, **Hero UI** for prebuilt components, and **React Router** for navigation.
- Code is organized by feature: `src/components/` (UI), `src/pages/` (screens), `src/layout/` (wrappers), `src/api/` (API logic), `src/utils/` (helpers), and `src/validation/` (Yup schemas).

## Key Workflows

- **Start dev server:** `yarn dev` (Vite, port 5173)
- **Build:** `yarn build`
- **Lint/Format:** `yarn lint`, `yarn format`
- **Tests:** (Jest config present, but no test folder found; check `jest.config.js` for details)
- **Environment:** Set `VITE_API_URL` in `.env` for API endpoints.

## Patterns & Conventions

- **Forms:** Use `react-hook-form` + Yup validation (see `src/validation/`).
- **API calls:** Centralized in `src/api/axios/apiClient.ts` and feature-specific files (e.g., `src/api/service/apiService.ts`).
- **Modals/Alerts:** Use Hero UI components (`@heroui/react`).
- **Routing:** Defined in `src/routes.tsx`.
- **State:** Prefer local state via hooks; minimal global state observed.
- **Styling:** Tailwind utility classes; custom CSS in `src/css/`.
- **Assets:** Images/icons in `src/assets/` and `public/`.

## Integration Points

- **External APIs:** All requests routed via `apiClient.ts` (Axios). Token management in `src/api/axios/tokenProvider.ts`.
- **File Uploads:** AWS S3 logic in `src/aws/s3FileUpload.ts`.
- **Localization:** i18n setup in `src/i18n.ts`, translations in `public/locales/`.

## Examples

- **Add a new page:** Create in `src/pages/`, add route in `src/routes.tsx`.
- **Add API logic:** Create in `src/api/service/` or similar, use `apiClient.ts`.
- **Add validation:** Create schema in `src/validation/`, use with `yupResolver` in forms.

## Tips

- Follow feature-based folder structure for new code.
- Use Hero UI and Tailwind for UI consistency.
- Always validate forms with Yup schemas.
- Keep API logic out of components; use hooks/services.

---

For questions about project-specific patterns, see `README.md` or ask for examples from `src/`.
