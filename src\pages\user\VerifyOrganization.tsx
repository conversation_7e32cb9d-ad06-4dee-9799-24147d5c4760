import CustomInput from "../../components/ui/CustomInput";
import {
  Autocomplete,
  AutocompleteItem,
  Card,
  DateInput,
  DatePicker,
  Divider,
  Textarea,
} from "@heroui/react";
import CustomButton from "../../components/ui/CustomButton";
import { Controller, useFormContext } from "react-hook-form";
import { useEffect, useState } from "react";

import {
  canadaProvincesAndTerritories,
  countryData,
  currencyCodeData,
  individualDocumentType,
  languageData,
} from "../../data/sampleData";
import CustomDivider from "../../components/ui/CustomDivider";
import { IoCloudUploadOutline } from "react-icons/io5";
import DocumentUploadBackSide from "../../components/ui/DocumentUploadBackSide";
import DocumentUplaodFrontSide from "../../components/ui/DocumentUplaodFrontSide";
import { IProviderInformationProps } from "../../types";
import { CalendarDate, getLocalTimeZone, today } from "@internationalized/date";
import { convertToInternationalizedDateTimeToReadble } from "../../utils/convertTime";
import CustomAutocomplete from "../../components/ui/CustomAutocomplete";

const VerifyOrganization = () => {
  const [documnetLabel, setDocumentLabel] = useState<string>("");

  const {
    register,
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<IProviderInformationProps>();

  const docType = watch("documents.type");
  const profileImage = watch("profilePicture");

  useEffect(() => {
    if (docType === "Passport") {
      setDocumentLabel("Passport No");
    } else if (docType === "Driving License") {
      setDocumentLabel("Driving License No");
    } else if (docType === "National Identity Card") {
      setDocumentLabel("NIC No");
    } else {
      setDocumentLabel("Document No");
    }
  }, [docType]);

  // const handleRemoveImage = () => {
  //   setValue("profilePicture", null);
  // };

  return (
    <>
      <Card radius="none" shadow="none" className="">
        <div className="mx-5 ">
          {/* GENERAL INFORMATION */}
          <div className="">
            {/* Profile image */}
            {/* <div className="flex flex-initial py-5 items-center gap-5">
              <div className="w-28 h-28 rounded-full bg-gray-200 dark:bg-darkModeBackground overflow-hidden">
                {profileImage ? (
                  <img
                    src={URL.createObjectURL(profileImage as File)}
                    alt="profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500 text-sm">
                    No Image
                  </div>
                )}
              </div>

              <div className="flex flex-col gap-2">
                <div className="flex flex-initial items-center gap-2">
                  <Controller
                    name="profilePicture"
                    control={control}
                    render={({ field }) => (
                      <label className="text-xs cursor-pointer bg-blue-600 text-white px-2 py-2 rounded-md shadow hover:bg-blue-700 transition">
                        Upload
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0] ?? null;
                            field.onChange(file);
                          }}
                        />
                      </label>
                    )}
                  />

                  {profileImage && (
                    <CustomButton
                      label="Remove"
                      size="sm"
                      color="danger"
                      onPress={handleRemoveImage}
                    />
                  )}
                </div>
                <small className="text-[10px] text-gray-500">
                  JPG, PNG, GIF. Max size: 5MB
                </small>
              </div>
            </div> */}

            {/* Input */}
            <div className="grid md:grid-cols-2 gap-5 mt-2">
              <div>
                <CustomInput
                  label="Business Name"
                  isRequired
                  radius="sm"
                  variant="bordered"
                  size="md"
                  placeholder="Enter business name"
                  isInvalid={!!errors?.name}
                  errorMessage={errors?.name?.message}
                  type="text"
                  {...register("name")}
                />
              </div>

              <div className="">
                <Controller
                  name="currencyCode"
                  control={control}
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      {...field}
                      onSelectionChange={(key) => field.onChange(key)}
                      selectedKey={field.value}
                      radius="sm"
                      labelPlacement="outside"
                      size="md"
                      variant="bordered"
                      label="Category"
                      placeholder="Search an category"
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                    >
                      {currencyCodeData.map((cur) => (
                        <AutocompleteItem key={cur.key}>
                          {cur.label}
                        </AutocompleteItem>
                      ))}
                    </Autocomplete>
                  )}
                />
              </div>

              <div>
                <CustomInput
                  type="text"
                  isRequired
                  radius="sm"
                  variant="bordered"
                  size="md"
                  label="Contact Number"
                  placeholder="Enter contact number"
                  description="Format: +1XXXXXXXXXX (no spaces)"
                  {...register("mobile")}
                  isInvalid={!!errors?.mobile}
                  errorMessage={errors?.mobile?.message}
                />
              </div>

              <div>
                <Controller
                  name={"dateOfBirth"}
                  control={control}
                  render={({ field }) => (
                    <DateInput
                      label="Business Registration Date"
                      variant="bordered"
                      labelPlacement="outside"
                      description="MM-DD-YYYY"
                      onChange={(val: CalendarDate) => {
                        const date =
                          convertToInternationalizedDateTimeToReadble(val);
                        field.onChange(date);
                      }}
                    />
                  )}
                />
              </div>

              {/* <div className="">
                <Controller
                  name="language"
                  control={control}
                  render={({ field, fieldState }) => (
                    <CustomAutocomplete
                      label="Language"
                      isRequired
                      placeholder="Select a language"
                      selectedKey={field.value}
                      defaultItems={languageData}
                      width="none"
                      onSelectionChange={(val) => {
                        field.onChange(val);
                      }}
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                    />
                  )}
                />
              </div> */}

              {/* <div className="">
                <Controller
                  name="currencyCode"
                  control={control}
                  render={({ field, fieldState }) => (
                    <CustomAutocomplete
                      label="Currency"
                      isRequired
                      placeholder="Search an Currency"
                      selectedKey={field.value}
                      defaultItems={currencyCodeData}
                      width="none"
                      onSelectionChange={(val) => {
                        field.onChange(val);
                      }}
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                    />
                  )}
                />
              </div> */}
            </div>

            {/* Bio (Textarea) */}
            {/* <div className="mt-4">
              <Textarea
                label="Business Bio"
                isClearable
                labelPlacement="outside"
                placeholder="Write a short business bio"
                variant="bordered"
                onClear={() => console.log("Bio cleared")}
                {...register("bio")}
                isInvalid={!!errors?.bio}
                errorMessage={errors?.bio?.message}
              />
            </div> */}
          </div>

          {/* ADDRESS INFORMATION */}
          <div className="mt-8">
            <div className="flex flex-initial justify-between items-center">
              <p className="text-subtitle3">Office Address </p>
            </div>
            {/* <Divider className="my-3" /> */}
            <CustomDivider />
            <div className="mt-12 mb-6">
              <CustomInput
                label="Address"
                radius="sm"
                variant="bordered"
                size="md"
                placeholder="Enter your address"
                type="text"
                {...register("address.addressLine1")}
                isInvalid={!!errors?.address?.addressLine1}
                errorMessage={errors?.address?.addressLine1?.message}
              />
            </div>
            <div className="grid grid-cols-2 gap-5 mt-2">
              <Controller
                name="address.country"
                control={control}
                render={({ field, fieldState }) => (
                  <Autocomplete
                    {...field}
                    onSelectionChange={(key) => field.onChange(key)}
                    selectedKey={field.value}
                    radius="sm"
                    labelPlacement="outside"
                    size="md"
                    variant="bordered"
                    label="Country"
                    placeholder="Enter your country"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                  >
                    {countryData.map((s) => (
                      <AutocompleteItem key={s.key}>{s.label}</AutocompleteItem>
                    ))}
                  </Autocomplete>
                )}
              />

              <Controller
                name="address.state"
                control={control}
                render={({ field, fieldState }) => (
                  <Autocomplete
                    {...field}
                    onSelectionChange={(key) => field.onChange(key)}
                    selectedKey={field.value}
                    radius="sm"
                    labelPlacement="outside"
                    size="md"
                    variant="bordered"
                    label="State"
                    placeholder="Enter your state"
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                  >
                    {canadaProvincesAndTerritories.map((s) => (
                      <AutocompleteItem key={s.key}>{s.label}</AutocompleteItem>
                    ))}
                  </Autocomplete>
                )}
              />

              <div>
                <CustomInput
                  radius="sm"
                  variant="bordered"
                  size="md"
                  label="City"
                  placeholder="Enter your city"
                  type="tel"
                  {...register("address.city")}
                  isInvalid={!!errors?.address?.city}
                  errorMessage={errors?.address?.city?.message}
                />
              </div>

              <div>
                <CustomInput
                  radius="sm"
                  variant="bordered"
                  size="md"
                  label="Postal Code"
                  placeholder="Enter postal code"
                  type="tel"
                  {...register("address.postalCode")}
                  isInvalid={!!errors?.address?.postalCode}
                  errorMessage={errors?.address?.postalCode?.message}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-5 "></div>
          </div>

          <div className="mt-8 mb-10">
            <div className="flex flex-initial justify-between items-center">
              <p className="text-subtitle3">Document Upload</p>
            </div>

            <div className="my-3">
              <CustomDivider />
            </div>

            <div>
              <div className="grid md:grid-cols-2 gap-5 mt-5">
                <Controller
                  name="documents.type"
                  control={control}
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      {...field}
                      onSelectionChange={(key) => field.onChange(key)}
                      selectedKey={field.value}
                      radius="sm"
                      labelPlacement="outside"
                      size="md"
                      variant="bordered"
                      label="Document Type"
                      placeholder="Select a document type"
                      isInvalid={!!fieldState.error}
                      errorMessage={fieldState.error?.message}
                    >
                      {individualDocumentType.map((s) => (
                        <AutocompleteItem key={s.key}>
                          {s.label}
                        </AutocompleteItem>
                      ))}
                    </Autocomplete>
                  )}
                />

                <CustomInput
                  label={documnetLabel}
                  radius="sm"
                  variant="bordered"
                  size="md"
                  placeholder="Enter number"
                  type="text"
                  {...register("documents.documentNo")}
                  isInvalid={!!errors?.documents?.documentNo}
                  errorMessage={errors?.documents?.documentNo?.message}
                />

                <div>
                  <Controller
                    name={"dateOfBirth"}
                    control={control}
                    render={({ field }) => (
                      <DateInput
                        label="Business Registration Date"
                        variant="bordered"
                        labelPlacement="outside"
                        description="MM-DD-YYYY"
                        onChange={(val: CalendarDate) => {
                          const date =
                            convertToInternationalizedDateTimeToReadble(val);
                          field.onChange(date);
                        }}
                      />
                    )}
                  />
                </div>

                <div>
                  <Controller
                    name={"dateOfBirth"}
                    control={control}
                    render={({ field }) => (
                      <DateInput
                        label="Business Registration Date"
                        variant="bordered"
                        labelPlacement="outside"
                        description="MM-DD-YYYY"
                        onChange={(val: CalendarDate) => {
                          const date =
                            convertToInternationalizedDateTimeToReadble(val);
                          field.onChange(date);
                        }}
                      />
                    )}
                  />
                </div>
              </div>

              <div className="grid xl:grid-cols-2 gap-5 my-5">
                <div>
                  <p className="text-subtitle1 my-3">Front Side</p>
                  <Controller
                    name="documents.front"
                    control={control}
                    render={({ field }) => (
                      <DocumentUplaodFrontSide
                        title="Upload Front Side"
                        icon={<IoCloudUploadOutline size={25} />}
                        value={field.value}
                        onChange={field.onChange}
                        error={!!errors?.documents?.front}
                      />
                    )}
                  />

                  <small className="text-error">
                    {errors?.documents?.front?.message}
                  </small>

                  <small className="flex flex-col text-gray-500">
                    Note: Make sure the entire front side is visible, clear, and
                    well-lit.
                  </small>
                </div>

                {docType !== "Passport" && (
                  <div>
                    <p className="text-subtitle1 my-3">Back Side</p>
                    <Controller
                      name="documents.back"
                      control={control}
                      render={({ field }) => (
                        <DocumentUploadBackSide
                          title="Uplaod Back Side"
                          icon={<IoCloudUploadOutline size={25} />}
                          value={field.value}
                          onChange={field.onChange}
                          error={!!errors?.documents?.back}
                        />
                      )}
                    />
                    <small className="text-error">
                      {errors?.documents?.back?.message}
                    </small>

                    <small className="flex flex-col text-gray-500">
                      Note: Ensure the back side is fully visible and all text
                      is readable.
                    </small>
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-5 mt-6"></div>
          </div>

          <div className="flex justify-end items-center mt-10 gap-5">
            <CustomButton
              type="submit"
              size="sm"
              label="Close"
              color="danger"
              variant="light"
            />
            <CustomButton
              type="submit"
              size="sm"
              label="Submit"
              color="primary"
              //   isLoading={isSuccess}
            />
          </div>
        </div>
      </Card>
    </>
  );
};

export default VerifyOrganization;
