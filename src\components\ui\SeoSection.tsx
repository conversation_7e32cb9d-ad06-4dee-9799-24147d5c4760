import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Divider } from "@heroui/react";

import { memo } from "react";
import { useFormContext } from "react-hook-form";
import CustomInput from "./CustomInput";
import { IServiceSubmitProps } from "../../types";
import CustomTextArea from "./CustomTextArea";

const SeoSection = memo(() => {
  const {
    register,
    watch,
    formState: { errors },
  } = useFormContext<IServiceSubmitProps>();

  return (
    <>
      <div className="flex flex-col gap-3">
        {/* <Divider className="-my-2" /> */}
        <CustomInput
          label="Meta Title"
          type="text"
          placeholder="Enter meta title"
          {...register("seo.0.metaTitle")}
          value={watch("seo.0.metaTitle")}
          isInvalid={!!errors?.seo?.[0]?.metaTitle}
          errorMessage={errors?.seo?.[0]?.metaTitle?.message}
        />
        <CustomInput
          size="md"
          label="Meta Tag"
          type="text"
          placeholder="Enter Tags"
          description="Enter comma separated tags (Ex: tag1, tag2, tag3)"
          {...register("seo.0.metaKeywords")}
          value={watch("seo.0.metaKeywords")}
          isInvalid={!!errors?.seo?.[0]?.metaKeywords}
          errorMessage={errors?.seo?.[0]?.metaKeywords?.message}
        />

        <CustomTextArea
          label="Meta Description"
          placeholder="Enter meta description"
          {...register("seo.0.metaDescription")}
          value={watch("seo.0.metaDescription")}
          isInvalid={!!errors?.seo?.[0]?.metaDescription}
          errorMessage={errors?.seo?.[0]?.metaDescription?.message}
        />
      </div>
    </>
  );
});

export default SeoSection;
