import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  InputOtp,
} from "@heroui/react";
import logo from "../../assets/newlogoNew.png";

const ConfirmCodePage = () => {
  return (
    <div className="min-h-screen grid grid-cols-1 laptop:grid-cols-2 desktop:grid-cols-3">
      {/* Right Side - Image */}
      <div className="hidden  desktop:col-span-2 laptop:flex justify-center items-center bg-blue-100">
        <img
          src="https://cdn.staging.gigmosaic.ca/service/vv.jpg-1753259572022"
          alt="Password Reset Illustration"
          className="object-cover w-full h-full"
        />
      </div>

      <div className="flex justify-center items-center bg-gray-50 p-4 dark:bg-darkModeBackgroundSecondary">
        <Card className="w-full max-w-md shadow-lg rounded-2xl  desktop:py-6 tablet:p-6 p-1">
          <img src={logo} alt="" className="w-[300px] h-[80px] mx-auto" />
          <CardHeader className="text-center flex flex-col items-center">
            <h2 className="text-2xl font-bold">Enter Verification Code</h2>
            <p className="text-sm text-gray-500 mt-1 text-start">
              Enter 6-digit Code Send to your email
            </p>
          </CardHeader>

          <CardBody className="flex items-center justify-center">
            <InputOtp
              size="lg"
              length={6}
              variant="bordered"
              color="primary"
              //   isInvalid
              //   errorMessage="Invalid OTP code"
            />
          </CardBody>

          <CardFooter className="flex flex-col space-y-3">
            <Button className="w-full" color="primary" radius="md">
              Get Reset Code
            </Button>

            {/* Left Side - Form */}
            <p className="text-xs text-gray-500 text-center">
              Don’t get a code?{" "}
              <a className="text-blue-500 font-medium">Resend code</a>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ConfirmCodePage;
