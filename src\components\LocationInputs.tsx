// import { useEffect, useRef, useState } from "react";
// import CustomInput from "./ui/CustomInput";
// import { CardBody, Divider } from "@heroui/react";
// import { GeocoderAutocomplete } from "@geoapify/geocoder-autocomplete";
// import "@geoapify/geocoder-autocomplete/styles/minimal.css";
// import L from "leaflet";
// import "leaflet/dist/leaflet.css";
// import "../css/location.css";
// import LocationPin from "../assets/location-pin.png";

// interface LocationInputsProps {
//   onChangeValue: (location: ILocationProps) => void;
//   data?: ILocationProps;
//   errors?: any;
// }

// export interface ILocationProps {
//   address: string;
//   country: string;
//   city: string;
//   state: string;
//   pinCode: string;
//   latitude: string;
//   longitude: string;
//   googleMapsPlaceId: string;
// }

// const LocationInputs = ({
//   onChangeValue,
//   data,
//   errors,
// }: LocationInputsProps) => {
//   const myAPIKey = import.meta.env.VITE_GEOLOCATION_API_KEY;
//   const mapRef = useRef<L.Map | null>(null);
//   const markerRef = useRef<L.Marker | null>(null);
//   const autocompleteRef = useRef<HTMLDivElement | null>(null);
//   const [address, setAddress] = useState("");
//   const [location, setLocation] = useState<ILocationProps>({
//     address: "",
//     country: "",
//     city: "",
//     state: "",
//     pinCode: "",
//     latitude: "",
//     longitude: "",
//     googleMapsPlaceId: "",
//   });

//   useEffect(() => {
//     setLocation(
//       data || {
//         address: "",
//         country: "",
//         city: "",
//         state: "",
//         pinCode: "",
//         latitude: "",
//         longitude: "",
//         googleMapsPlaceId: "",
//       }
//     );

//     setAddress(data?.address || "");
//   }, [data]);

//   useEffect(() => {
//     if (!mapRef.current) {
//       mapRef.current = L.map("map", { zoomControl: false }).setView(
//         [38.9088, -77.0234],
//         12
//       );

//       const isRetina = L.Browser.retina;
//       const baseUrl = `https://maps.geoapify.com/v1/tile/osm-bright/{z}/{x}/{y}.png?apiKey=${myAPIKey}`;
//       const retinaUrl = `https://maps.geoapify.com/v1/tile/osm-bright/{z}/{x}/{y}@2x.png?apiKey=${myAPIKey}`;

//       L.tileLayer(isRetina ? retinaUrl : baseUrl, {
//         attribution:
//           'Powered by <a href="https://www.geoapify.com/" target="_blank">Geoapify</a> | <a href="https://www.openstreetmap.org/copyright" target="_blank">© OpenStreetMap</a>',
//         maxZoom: 20,
//       }).addTo(mapRef.current);

//       L.control
//         .zoom({
//           position: "bottomright",
//         })
//         .addTo(mapRef.current);
//     }

//     if (data?.coordinates?.latitude && data?.coordinates?.longitude) {
//       const lat = parseFloat(data?.coordinates?.latitude);
//       const lon = parseFloat(data?.coordinates?.longitude);

//       if (!isNaN(lat) && !isNaN(lon)) {
//         mapRef.current.setView([lat, lon], 15); // Move the map to the new location

//         // Remove previous marker if exists
//         if (markerRef.current) {
//           markerRef.current.remove();
//         }

//         const customIcon = L.icon({
//           iconUrl: LocationPin,
//           iconSize: [40, 40],
//           iconAnchor: [20, 40],
//           popupAnchor: [0, -40],
//         });

//         // Add a new marker
//         markerRef.current = L.marker([lat, lon], { icon: customIcon })
//           .addTo(mapRef.current)
//           .bindPopup(data.address)
//           .openPopup();
//       }
//     }

//     // Initialize Geocoder Autocomplete only once
//     if (autocompleteRef.current && !autocompleteRef.current.hasChildNodes()) {
//       const autocompleteInput = new GeocoderAutocomplete(
//         autocompleteRef.current,
//         myAPIKey,
//         {}
//       );

//       autocompleteInput.setValue(address);

//       autocompleteInput.on("select", (location) => {
//         if (!mapRef.current) return;

//         const { lat, lon, formatted, country, city, state, postcode } =
//           location.properties;

//         // Remove previous marker if exists
//         if (markerRef.current) {
//           markerRef.current.remove();
//         }

//         // Set new marker

//         const customIcon = L.icon({
//           iconUrl: LocationPin,
//           iconSize: [40, 40],
//           iconAnchor: [20, 40],
//           popupAnchor: [0, -40],
//         });

//         markerRef.current = L.marker([lat, lon], { icon: customIcon })
//           .addTo(mapRef.current)
//           .bindPopup(formatted)
//           .openPopup();
//         mapRef.current.panTo([lat, lon]);

//         // Get place ID via reverse geocoding
//         const placeIdUrl = `https://api.geoapify.com/v1/geocode/reverse?lat=${lat}&lon=${lon}&apiKey=${myAPIKey}`;
//         fetch(placeIdUrl)
//           .then((response) => response.json())
//           .then((data) => {
//             if (data.features && data.features.length > 0) {
//               const placeId = data.features[0].properties.place_id;
//               setLocation({
//                 address: formatted || "",
//                 country: country || "",
//                 city: city || "",
//                 state: state || "",
//                 pinCode: postcode || "",
//                 latitude: lat,
//                 longitude: lon,
//                 googleMapsPlaceId: placeId,
//               });
//               onChangeValue({
//                 address: formatted || "",
//                 country: country || "",
//                 city: city || "",
//                 state: state || "",
//                 pinCode: postcode || "",
//                 latitude: lat,
//                 longitude: lon,
//                 googleMapsPlaceId: placeId,
//               });
//             }
//           })
//           .catch((error) => console.error("Error fetching place ID:", error));
//       });

//       autocompleteInput.on("input", (e: any) => {
//         if (e !== undefined) {
//           setLocation((prevState) => ({
//             ...prevState,
//             address: e,
//           }));
//         } else {
//           console.error("Input event target is undefined");
//         }
//       });
//       autocompleteInput.setValue(address);
//     }

//     return () => {
//       if (mapRef.current) {
//         mapRef.current.remove();
//         mapRef.current = null;
//       }
//     };
//   }, [data]);

//   useEffect(() => {
//     onChangeValue(location);
//   }, [location]);

//   return (
//     <>
//       <CardBody className="gap-6">
//         <Divider className="-my-2" />
//         <label className="text-sm -mb-4" htmlFor="location">
//           Address <span className="text-red-400">*</span>
//         </label>
//         <div>
//           <div>
//             <div
//               ref={autocompleteRef}
//               // defaultValue={location?.address}
//               className="relative geoapify-autocomplete-input dark:bg-transparent"
//             ></div>
//             <span className="text-gray-400 text-xs -mt-5">
//               Please Select Address
//             </span>
//           </div>
//           {errors?.address && (
//             <span className="text-error mt-5">{errors?.address}</span>
//           )}
//         </div>

//         <div className="grid lg:grid-cols-2 gap-6">
//           <div>
//             <CustomInput
//               label="Country"
//               isRequired={true}
//               type="text"
//               placeholder="Enter country"
//               name={location?.country}
//               value={location?.country}
//               onValueChange={(e) => {
//                 setLocation({
//                   ...location,
//                   country: e,
//                 });
//               }}
//             />
//             {errors?.country && (
//               <span className="text-error -mt-5">{errors?.country}</span>
//             )}
//           </div>

//           <div>
//             <CustomInput
//               label="City"
//               isRequired={true}
//               type="text"
//               placeholder="Enter city"
//               name={location?.city}
//               value={location?.city}
//               onValueChange={(e) => {
//                 setLocation({
//                   ...location,
//                   city: e,
//                 });
//               }}
//             />
//             {errors?.city && (
//               <small className="text-error -mt-5">{errors?.city}</small>
//             )}
//           </div>
//         </div>

//         <div className="grid lg:grid-cols-2 gap-6">
//           <div>
//             <CustomInput
//               label="State"
//               isRequired={true}
//               type="text"
//               placeholder="Enter state"
//               name={location?.state}
//               value={location?.state}
//               onValueChange={(e) => {
//                 setLocation({
//                   ...location,
//                   state: e,
//                 });
//               }}
//             />
//             {errors?.state && (
//               <small className="text-error -mt-5">{errors?.state}</small>
//             )}
//           </div>
//           <div>
//             <CustomInput
//               label="PinCode"
//               isRequired={true}
//               type="text"
//               placeholder="Enter pinCode"
//               name={location?.pinCode}
//               value={location?.pinCode}
//               onValueChange={(e) => {
//                 setLocation({
//                   ...location,
//                   pinCode: e,
//                 });
//               }}
//             />
//             {errors?.pinCode && (
//               <small className="text-error -mt-5">{errors?.pinCode}</small>
//             )}
//           </div>
//         </div>

//         <div>
//           <CustomInput
//             label="Google Maps Place ID"
//             isRequired={false}
//             type="text"
//             placeholder="Enter maps place id"
//             name={location?.googleMapsPlaceId}
//             value={location?.googleMapsPlaceId}
//             onValueChange={(e) => {
//               setLocation({
//                 ...location,
//                 googleMapsPlaceId: e,
//               });
//             }}
//           />
//           {errors?.googleMapsPlaceId && (
//             <small className="text-error -mt-5">
//               {errors?.googleMapsPlaceId}
//             </small>
//           )}
//         </div>

//         <div className="grid lg:grid-cols-2 gap-6">
//           <div>
//             <CustomInput
//               label="Latitude"
//               type="text"
//               isRequired={false}
//               placeholder="Enter latitude"
//               name={location?.latitude}
//               value={
//                 location?.latitude ?? location?.coordinates?.latitude ?? ""
//               }
//               onValueChange={(e) => {
//                 setLocation({
//                   ...location,
//                   latitude: e,
//                 });
//               }}
//             />
//             {errors?.latitude && (
//               <small className="text-error -mt-5">{errors?.latitude}</small>
//             )}
//           </div>
//           <div>
//             <CustomInput
//               label="Longitude"
//               type="text"
//               isRequired={false}
//               placeholder="Enter longitude"
//               name={location?.longitude}
//               value={
//                 location?.longitude ?? location?.coordinates?.longitude ?? ""
//               }
//               onValueChange={(e) => {
//                 setLocation({
//                   ...location,
//                   longitude: e,
//                 });
//               }}
//             />
//             {errors?.longitude && (
//               <small className="text-error -mt-5">{errors?.longitude}</small>
//             )}
//           </div>
//         </div>

//         {/* Map Container */}
//         <div
//           id="map"
//           style={{
//             height: "400px",
//             width: "100%",
//             border: "2px solid #ccc",
//             zIndex: 0,
//           }}
//         ></div>
//       </CardBody>
//     </>
//   );
// };

// export default LocationInputs;

import { useEffect, useRef, useState } from "react";
import CustomInput from "./ui/CustomInput";
import { Card, CardBody, CardFooter, CardHeader, Divider } from "@heroui/react";
import { GeocoderAutocomplete } from "@geoapify/geocoder-autocomplete";
import "@geoapify/geocoder-autocomplete/styles/minimal.css";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "../css/location.css";
import LocationPin from "../assets/location-pin.png";
import { IServiceSubmitProps } from "../types";
import { useFormContext } from "react-hook-form";

const LocationInputs = () => {
  const myAPIKey = import.meta.env.VITE_GEOLOCATION_API_KEY;
  const myCountryCode = import.meta.env.VITE_COUNTRY_CODE;
  const myCountry = import.meta.env.VITE_COUNTRY;

  const containerRef = useRef<HTMLDivElement | null>(null);

  const [latitude, setLatitude] = useState<number>(45.390735);
  const [longitude, setLongitude] = useState<number>(-75.72876);
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);

  const {
    watch,
    trigger,
    register,
    setValue,
    formState: { errors },
  } = useFormContext<IServiceSubmitProps>();

  const selectCity = watch("location.0.city");
  const selectState = watch("location.0.state");

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setSuggestions([]); // Close suggestions
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const map = L.map("map").setView([latitude, longitude], 18);

    const customIcon = L.icon({
      iconUrl: LocationPin,
      iconSize: [40, 40],
      iconAnchor: [20, 40],
      popupAnchor: [0, -40],
    });

    L.tileLayer(
      `https://maps.geoapify.com/v1/tile/osm-carto/{z}/{x}/{y}.png?apiKey=${myAPIKey}`,
      {
        attribution:
          '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',
        maxZoom: 20,
      }
    ).addTo(map);

    const marker = L.marker([latitude, longitude], {
      icon: customIcon,
      draggable: true,
    })
      .addTo(map)
      .bindPopup(
        [selectState, selectCity].filter(Boolean).join(", ") ||
          "Drag me to set latitude and longitude"
      )
      .openPopup();

    marker.on("dragend", function () {
      const latlng = marker.getLatLng();
      setValue("location.0.latitude", Number(latlng.lat.toFixed(6)));
      setValue("location.0.longitude", Number(latlng.lng.toFixed(6)));
    });

    return () => {
      map.remove();
    };
  }, [longitude, latitude]);

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (query.length < 2) return;

      const res = await fetch(
        `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(
          query
        )}&limit=10&filter=countrycode:${myCountryCode}&apiKey=${myAPIKey}`
      );
      const data = await res.json();
      setSuggestions(data.features || []);
    };

    const debounce = setTimeout(fetchSuggestions, 300); // debounce
    return () => clearTimeout(debounce);
  }, [query]);

  const selectAddressSuggestion = (suggestion: any) => {
    const selected = suggestion?.properties;
    if (!selected) return;

    setQuery(selected.formatted || "");

    setValue("location.0.address", selected.address_line1 || "");
    setValue("location.0.country", selected.country || "");
    setValue("location.0.city", selected.city || "");
    setValue("location.0.state", selected.state || "");
    setValue("location.0.pinCode", selected.postcode || "");
    setValue("location.0.latitude", Number(selected.lat) || 0);
    setValue("location.0.longitude", Number(selected.lon) || 0);
    setValue("location.0.googleMapsPlaceId", selected.place_id || "");
    setLongitude(selected.lon);
    setLatitude(selected.lat);
    setSuggestions([]);
    trigger("location.0");
  };

  return (
    <>
      <div ref={containerRef} className="relative">
        <CustomInput
          label="Address"
          type="text"
          value={watch("location.0.address")}
          description={`Search or type address only in ${myCountry}`}
          onChange={(e) => {
            setQuery(e.target.value);
            setValue("location.0.address", e.target.value);
          }}
          placeholder="Search or type address"
          isInvalid={!!errors?.location?.[0]?.address}
          errorMessage={errors?.location?.[0]?.address?.message}
        />

        {suggestions.length > 0 && (
          <div className="absolute z-50 max-w-[1000px] -mt-8 right-1 left-1 bg-gray-100 border-gray-400 dark:bg-gray-900  rounded-md max-h-90 overflow-auto mx-3 shadow-lg border-1 dark:border-gray-800">
            {suggestions.map((place, idx) => (
              <div
                key={idx}
                onClick={() => selectAddressSuggestion(place)}
                className="px-4 py-2 cursor-pointer hover:bg-gray-400 dark:hover:bg-sideBarBackground text-sm"
              >
                {place?.properties?.formatted}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="grid lg:grid-cols-2 gap-3  mt-2">
        <CustomInput
          label="Country"
          type="text"
          placeholder="Enter country"
          description="Read Only field"
          isReadOnly={true}
          value={watch("location.0.country")}
          {...register("location.0.country")}
          isInvalid={!!errors?.location?.[0]?.country}
          errorMessage={errors?.location?.[0]?.country?.message}
        />

        <CustomInput
          label="City"
          type="text"
          isReadOnly={true}
          description="Read Only field"
          placeholder="Enter city"
          value={watch("location.0.city")}
          {...register("location.0.city")}
          isInvalid={!!errors?.location?.[0]?.city}
          errorMessage={errors?.location?.[0]?.city?.message}
        />

        <CustomInput
          label="State"
          type="text"
          isReadOnly={true}
          description="Read Only field"
          placeholder="Enter state"
          value={watch("location.0.state")}
          {...register("location.0.state")}
          isInvalid={!!errors?.location?.[0]?.state}
          errorMessage={errors?.location?.[0]?.state?.message}
        />

        <CustomInput
          label="Postal Code"
          type="text"
          description="Read Only field"
          isReadOnly={true}
          placeholder="Enter pinCode"
          value={watch("location.0.pinCode")}
          {...register("location.0.pinCode")}
          isInvalid={!!errors?.location?.[0]?.pinCode}
          errorMessage={errors?.location?.[0]?.pinCode?.message}
        />

        {/* <CustomInput
                    label="Google Maps Place ID"
                    type="text"
                    placeholder="Enter maps place id"
                    value={watch("location.0.googleMapsPlaceId")}
                    {...register("location.0.googleMapsPlaceId")}
                    isInvalid={!!errors?.location?.[0]?.googleMapsPlaceId}
                    errorMessage={
                      errors?.location?.[0]?.googleMapsPlaceId?.message
                    }
                  />

                  <CustomNumberInput
                    label="Latitude"
                    placeholder="Enter latitude"
                    formatOptions={{ maximumFractionDigits: 10 }}
                    value={watch("location.0.latitude")}
                    {...register("location.0.latitude")}
                    isInvalid={!!errors?.location?.[0]?.latitude}
                    errorMessage={errors?.location?.[0]?.latitude?.message}
                  />

                  <CustomNumberInput
                    label="Longitude"
                    formatOptions={{ maximumFractionDigits: 10 }}
                    placeholder="Enter longitude"
                    value={watch("location.0.longitude")}
                    {...register("location.0.longitude")}
                    isInvalid={!!errors?.location?.[0]?.longitude}
                    errorMessage={errors?.location?.[0]?.longitude?.message}
                  /> */}
      </div>

      <div className="mt-4">
        {/* Map Container */}
        <div
          id="map"
          style={{
            height: "500px",
            width: "100%",
            border: "2px solid #ccc",
            zIndex: 0,
            borderRadius: "10px",
          }}
        ></div>
      </div>
    </>
  );
};

export default LocationInputs;
