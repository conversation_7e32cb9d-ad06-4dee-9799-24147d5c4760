import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Chip, Divider } from "@heroui/react";
import { Hi<PERSON>he<PERSON> } from "react-icons/hi";
import {
  TbRosetteDiscountCheckFilled,
  TbRosetteDiscountOff,
} from "react-icons/tb";
import CustomButton from "./CustomButton";
import { IPackageProps } from "../../types";
import { useEffect, useState } from "react";
import CustomDivider from "./CustomDivider";
import { useTheme } from "@heroui/use-theme";
import { MdDiscount } from "react-icons/md";

const features = [
  "Everything in Pro",
  "Dedicated account manager",
  "Advanced team collaboration ",
  "API access",
];

const PackageDisplayUI = ({ data }: { data: IPackageProps[] }) => {
  console.log("DATATTA: ", data);
  const { theme } = useTheme();
  if (data?.length === 0) return;

  // Convert includes string to array and filter
  const formatIncludes = (includes: { [key: string]: string }) => {
    if (!includes) return [];
    return Object.values(includes) // get all values from object
      .map((item) => item.trim()) // remove extra spaces
      .filter((item) => item !== ""); // keep only non-empty
  };

  const calculateDiscount = (price: string, discount: string, type: string) => {
    if (!price || !discount || !type) return price;

    const priceCon = Number(price);
    const discountCon = Number(discount);

    let finalPrice = 0;

    if (type === "amount") {
      finalPrice = priceCon - discountCon;
    } else {
      finalPrice = priceCon - (priceCon * discountCon) / 100;
    }

    return Number(finalPrice.toFixed(2));
  };

  return (
    <>
      {data?.map((item, index) => (
        <Card
          key={index}
          radius="sm"
          isDisabled={item?.isSoldOut ? true : false}
          shadow="none"
          className="border-2 border-gray-200 shadow-sm dark:border-gray-700 light:bg-slate-50"
        >
          <CardHeader className="py-2 px-3  flex flex-col items-start gap-2 ">
            <div className="flex items-center justify-between w-full ">
              <h2 className="text-2xl font-bold text-primary line-clamp-1">
                {item?.packageName || " No Name"}
              </h2>
              {item.isSoldOut ? (
                <Chip
                  color="danger"
                  variant={theme === "dark" ? "faded" : "flat"}
                  size="md"
                  startContent={
                    <TbRosetteDiscountOff
                      className={
                        theme === "dark" ? "text-lg text-red-500" : "text-lg"
                      }
                    />
                  }
                  classNames={{
                    content:
                      "text-md font-semibold text-red-700 dark:text-gray-300",
                  }}
                >
                  Sold Out
                </Chip>
              ) : item.isDiscount ? (
                <Chip
                  color="success"
                  variant={theme === "dark" ? "faded" : "flat"}
                  size="md"
                  startContent={
                    <TbRosetteDiscountCheckFilled
                      className={
                        theme === "dark" ? "text-lg text-green-500" : "text-lg"
                      }
                    />
                  }
                  classNames={{
                    content:
                      "text-md font-semibold text-green-800 dark:text-gray-300",
                  }}
                >
                  {item?.discount?.valueType === "percentage"
                    ? `${Number(item?.discount?.amount) || 0}% Save`
                    : `$${Number(item?.discount?.amount) || 0} Save`}
                </Chip>
              ) : null}
            </div>

            <div className="flex justify-between items-center w-full">
              <div className="flex items-baseline text-white">
                <p className="text-2xl font-extrabold text-gray-600 dark:text-gray-200">
                  $
                  {calculateDiscount(
                    item?.price,
                    item?.discount?.amount,
                    item?.discount?.valueType
                  )}
                </p>
                <p className="text-md font-medium text-gray-500 line-through ml-2">
                  ${item?.price || 0}
                </p>
              </div>
              {/* <p className="flex">End offer: 23h 34m 23s</p> */}
            </div>
          </CardHeader>

          <CardBody>
            <ul className="space-y-1.5 -mt-3">
              <Divider />
              {formatIncludes(item?.includes).map((inc, i) => (
                <li key={i} className="flex items-center space-x-3">
                  <HiCheck className="text-green-500 w-5 h-5 flex-shrink-0" />
                  <span className="text-gray-700 dark:text-gray-200 text-body1">
                    {inc}
                  </span>
                </li>
              ))}
            </ul>
            {item?.isSoldOut ? (
              <CustomButton
                label="Sold out"
                variant="flat"
                color="danger"
                className="mt-3"
                isDisabled={true}
              />
            ) : (
              <CustomButton
                label="Select Package"
                variant="flat"
                color="secondary"
                className="mt-3"
                // size="sm"
              />
            )}
          </CardBody>
        </Card>
      ))}
    </>
  );
};

export default PackageDisplayUI;
