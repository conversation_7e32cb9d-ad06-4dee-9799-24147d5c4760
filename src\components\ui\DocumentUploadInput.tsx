import { ReactNode, useState, ChangeEvent } from "react";
import { IoCloudUploadOutline } from "react-icons/io5";

interface DocumentUploadInputProps {
  title: string;
  subText: string;
  icon?: ReactNode;
}

const DocumentUploadInput = ({
  title,
  subText,
  icon,
}: DocumentUploadInputProps) => {
  const [filePreview, setFilePreview] = useState<string | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const previewUrl = URL.createObjectURL(file);
      setFilePreview(previewUrl);
    }
  };

  return (
    <div>
      <label
        htmlFor="uploadFile1"
        className="bg-white text-slate-500 font-semibold text-base rounded max-w-md h-52 flex flex-col items-center justify-center cursor-pointer border-2 border-gray-300 border-dashed mx-auto"
      >
        {filePreview ? (
          <img
            src={filePreview}
            alt="Preview"
            className="w-full h-full object-cover rounded"
          />
        ) : (
          <>
            {icon || <IoCloudUploadOutline size={24} />}
            <span>{title}</span>
            <p className="text-xs font-medium text-slate-400 mt-2">
              {subText} PNG, JPG, SVG, WEBP, and GIF are allowed.
            </p>
          </>
        )}
        <input
          type="file"
          id="uploadFile1"
          className="hidden"
          accept="image/png, image/jpeg, image/svg+xml, image/webp, image/gif"
          onChange={handleFileChange}
        />
      </label>
    </div>
  );
};

export default DocumentUploadInput;
