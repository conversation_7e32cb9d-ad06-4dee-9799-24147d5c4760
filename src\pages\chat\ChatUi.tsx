import { ChatProvider, Chat, initChatConfig } from "@pubuduth-aplicy/chat-ui";
// import { useAuth } from "react-oidc-context";

const ChatUi = () => {
  // const auth = useAuth();

   initChatConfig({
    apiUrl: `${import.meta.env.VITE_BACKEND_PORT}/chat`,
    role: 'provider',
    cdnUrl: import.meta.env.VITE_S3_CDN_URL,
    webSocketUrl: `${import.meta.env.VITE_WEBSOCKET_PORT}`
  });
  return (
    <div>
      <ChatProvider userId="G_UID_62">
      {/* <ChatProvider userId="663767972ea46a2a2ea14b9b"> */}
        <Chat />
      </ChatProvider>
    </div>
  );
};

export default ChatUi;
