// import { useState } from "react";
// import { getCurrentUser, setUpTOTP, verifyTOTPSetup } from "aws-amplify/auth";
// import { useAuth } from "react-oidc-context";

// export default function ManageMFA() {
//   const [qrCode, setQrCode] = useState<string | null>(null);
//   const [status, setStatus] = useState("");
//   const [error, setError] = useState("");
//   const [code, setCode] = useState("");
//   const auth = useAuth();

//   const enableMFA = async () => {
//     setError("");
//     try {
//       const user = await getCurrentUser();
//       console.log("Current User:", user);
//       auth.

//       const secret = await setUpTOTP(user);
//       console.log("TOTP Secret:", secret);

//       const issuer = "YourAppName";
//       const qrCodeUrl = `otpauth://totp/${issuer}:${user.username}?secret=${secret}&issuer=${issuer}`;

//       setQrCode(qrCodeUrl);
//       setStatus(
//         "Scan this QR code with your authenticator app, then enter the 6-digit code below."
//       );
//     } catch (err) {
//       console.error("Error setting up MFA:", err);
//       setError("Failed to start MFA setup. Please try again.");
//       setStatus("");
//     }
//   };

//   const verifyMFA = async () => {
//     try {
//       await verifyTOTPSetup({ code });
//       setStatus("MFA successfully enabled 🎉");
//       setError("");
//     } catch (err) {
//       console.error("Error verifying MFA:", err);
//       setError("Invalid code, please try again.");
//     }
//   };

//   return (
//     <div>
//       <button onClick={enableMFA}>Enable MFA</button>

//       {qrCode && (
//         <div>
//           <p>{status}</p>
//           <img
//             src={`https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(
//               qrCode
//             )}&size=200x200`}
//             alt="QR Code"
//           />
//           <input
//             type="text"
//             placeholder="Enter 6-digit code"
//             value={code}
//             onChange={(e) => setCode(e.target.value)}
//           />
//           <button onClick={verifyMFA}>Verify</button>
//         </div>
//       )}

//       {error && <p style={{ color: "red" }}>{error}</p>}
//     </div>
//   );
// }
