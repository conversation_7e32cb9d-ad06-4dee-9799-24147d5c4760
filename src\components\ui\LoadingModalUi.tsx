interface LoadingModalUiProps {
  label?: string;
  description?: string;
}

const LoadingModalUi = ({ label, description }: LoadingModalUiProps) => {
  return (
    <div>
      <div className="flex flex-col items-center justify-center laptop:h-[600px] space-y-4">
        <div className="w-10 h-10 animate-spin rounded-full border-4 border-t-primary"></div>
        <p className="mt-3 text-gray-600 text-sm font-medium">{label}</p>
        <p className="w-1/2 text-center text-gray-600">{description}</p>
      </div>
    </div>
  );
};

export default LoadingModalUi;
