import CustomInput from "../../components/ui/CustomInput";
import {
  addToast,
  Autocomplete,
  AutocompleteItem,
  Card,
  DatePicker,
  Divider,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  useDisclosure,
  DateInput,
} from "@heroui/react";
import CustomButton from "../../components/ui/CustomButton";
import { Controller, useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { useAuth } from "react-oidc-context";
import {
  CalendarDate,
  getLocalTimeZone,
  parseDate,
  today,
} from "@internationalized/date";
import { useFetchUserDetailsById } from "../../hooks/queries/useFetchData";
import { IProviderInformationProps } from "../../types";
import { providerDefaultValue } from "../../utils/defaultValue";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  canadaProvincesAndTerritories,
  countryData,
  currencyCodeData,
  languageData,
} from "../../data/sampleData";
import { FaRegEdit } from "react-icons/fa";
import { convertToInternationalizedDateTimeToReadble } from "../../utils/convertTime";
import CustomAutocomplete from "../../components/ui/CustomAutocomplete";
import {
  firstLetterUpperCase,
  singleFileUplaodHelper,
} from "../../utils/common";
import LoadingModalUi from "../../components/ui/LoadingModalUi";
import { editProviderSchema } from "../../validation/EditProvideSchema";
import { useUpdateUserprofile } from "../../hooks/mutations/useUpdateData";

const EditProviderDetails = () => {
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
  const auth = useAuth();
  const [image, setImage] = useState<string | null>(null);
  const [loading, setloading] = useState(false);

  const id = auth.user?.profile?.preferred_username || "";

  const { data, refetch } = useFetchUserDetailsById(id);

  const userData = data?.user || {};

  console.log("Edit get user data: ", userData);

  const [date, setDate] = useState<ReturnType<typeof parseDate> | null>(
    parseDate("1990-12-01")
  );
  console.log("EDIT Data: ", data);
  const { mutate } = useUpdateUserprofile();

  const form = useForm<IProviderInformationProps>({
    defaultValues: providerDefaultValue,
    shouldUnregister: false,
    resolver: yupResolver(editProviderSchema),
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const {
    register,
    control,
    setValue,
    watch,
    formState: { errors },
  } = form;

  const handleRemoveImage = () => setImage(null);

  const isDocUpload = watch("IsDocUploaded");
  console.log("isDocUpload: ", isDocUpload);

  useEffect(() => {
    if (isOpen && id) {
      refetch();

      const fullAddress = `${userData?.address?.addressLine1 || ""} ${
        userData?.address?.addressLine2 || ""
      }`.trim();

      setValue("name", userData?.name || "");

      setValue("email", auth.user?.profile.email || "");
      setValue("mobile", userData?.mobile || "");
      setValue("dateOfBirth", userData?.dateOfBirth || "");
      setValue("bio", userData?.bio || "");
      setValue("address.addressLine1", fullAddress || "");
      setValue("address.country", userData?.address?.country || "");
      setValue("address.state", userData?.address?.state || "");
      setValue("address.city", userData?.address?.city || "");
      setValue("address.postalCode", userData?.address?.postalCode || "");
      setValue("language", userData?.language || "");
      setValue("profilePicture", userData?.profilePicture || "");
      setValue("currencyCode", userData?.currencyCode || "");
      setValue("IsDocUploaded", userData?.IsDocUploaded || false);
      setValue("IsDocVerified", userData?.IsDocVerified || false);
      setDate(
        auth.user?.profile.birthdate
          ? parseDate(auth.user?.profile.birthdate)
          : null
      );

      setImage(userData?.profilePicture);
    }
  }, [isOpen, id, refetch, userData]);

  const onSubmit = async (data: IProviderInformationProps) => {
    setloading(true);
    try {
      let profileImage: string = "";

      const providerFolder = `${userData.userId}-${userData.name}`;
      const uid = auth.user?.profile?.preferred_username || "N/A";

      if (!uid) {
        addToast({
          title: "User ID not found",
          description: "Something went wrong.",
          radius: "md",
        });
        return;
      }

      if (image instanceof File) {
        profileImage = await singleFileUplaodHelper({
          file: image,
          baseFolder: "provider",
          mainFolder: "profile-picture",
          subFolder: providerFolder,
          errorMessageType: "Image",
        });
      } else {
        profileImage = image ?? "";
      }

      const profilePayload = {
        ...data,
        // dateOfBirth:
        //   data?.dateOfBirth === "" ? userData?.dateOfBirth : data?.dateOfBirth,
        currencyCode: data?.currencyCode.toUpperCase(),
        profilePicture: profileImage,
        language: firstLetterUpperCase(data?.language),
      };

      console.log("Payload: ", profilePayload);
      await mutate({ id: uid, data: profilePayload });
      addToast({
        title: "Update Success",
        description: "Profile updated successfully",
        radius: "md",
        color: "success",
      });
      onClose();
    } catch (error) {
      console.log("Error: ", error);
    } finally {
      setloading(false);
    }
  };
  console.log("Watch: ", watch());
  return (
    <>
      <FaRegEdit
        onClick={onOpen}
        className="cursor-pointer text-gray-500 hover:text-primary"
        size={18}
      />
      {/* TODO: Scroll beheavor chnage */}
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        size="5xl"
        // scrollBehavior="inside"
        className="p-5"
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader> Edit Provider Details</ModalHeader>
              <ModalBody>
                <Card radius="none" shadow="none" className="">
                  <div className="mx-5 ">
                    {loading ? (
                      <LoadingModalUi label="Uploading...." />
                    ) : (
                      <form onSubmit={form.handleSubmit(onSubmit)}>
                        <div>
                          {/* Profile image */}
                          <div className="flex flex-initial py-5 items-center gap-5">
                            <div className="w-28 h-28 rounded-full bg-gray-200 dark:bg-darkModeBackground overflow-hidden">
                              {image ? (
                                <img
                                  src={
                                    typeof image === "string"
                                      ? image
                                      : image instanceof File
                                      ? URL.createObjectURL(image)
                                      : undefined
                                  }
                                  alt="profile"
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center text-gray-500 text-sm">
                                  No Image
                                </div>
                              )}
                            </div>

                            <div className="flex flex-col gap-2">
                              <div className="flex flex-initial items-center gap-2">
                                <Controller
                                  name="profilePicture"
                                  control={control}
                                  render={({ field }) => (
                                    <label className="text-xs cursor-pointer bg-blue-600 text-white px-2 py-2 rounded-md shadow hover:bg-blue-700 transition">
                                      Upload
                                      <input
                                        type="file"
                                        accept="image/*"
                                        className="hidden"
                                        onChange={(e) => {
                                          const file =
                                            e.target.files?.[0] ?? null;
                                          field.onChange(file);
                                          setImage(file);
                                        }}
                                      />
                                    </label>
                                  )}
                                />

                                {image && (
                                  <CustomButton
                                    label="Remove"
                                    size="sm"
                                    color="danger"
                                    onPress={handleRemoveImage}
                                  />
                                )}
                              </div>
                              <small className="text-[10px] text-gray-500">
                                JPG, PNG, GIF. Max size: 5MB
                              </small>
                            </div>
                          </div>

                          {/* Input */}
                          <div className="grid md:grid-cols-2 gap-5 mt-2">
                            <div>
                              <CustomInput
                                isRequired
                                label="Name"
                                radius="sm"
                                variant="bordered"
                                size="md"
                                placeholder="Enter your full name"
                                value={watch("name")}
                                isInvalid={!!errors?.name}
                                errorMessage={errors?.name?.message}
                                type="text"
                                {...register("name")}
                                isDisabled={isDocUpload}
                              />
                            </div>
                            {/* TODO: Need fix validation . spance can allow */}
                            <div>
                              <CustomInput
                                isRequired
                                type="text"
                                radius="sm"
                                variant="bordered"
                                size="md"
                                label="Mobile Number"
                                value={watch("mobile")}
                                placeholder="Enter your phone number"
                                {...register("mobile")}
                                isInvalid={!!errors?.mobile}
                                errorMessage={errors?.mobile?.message}
                              />
                            </div>

                            <div>
                              <Controller
                                name="dateOfBirth"
                                control={control}
                                defaultValue={userData?.dateOfBirth || ""}
                                render={({ field }) => (
                                  // <DatePicker
                                  //   isRequired
                                  //   label="Date of Birth"
                                  //   aria-label="Select date"
                                  //   maxValue={today(getLocalTimeZone())}
                                  //   variant="bordered"
                                  //   labelPlacement="outside"
                                  //   value={
                                  //     field.value
                                  //       ? parseDate(field.value)
                                  //       : null
                                  //   }
                                  //   placeholder="Enter your date of birth"
                                  // onChange={(val: CalendarDate) => {
                                  //   const date =
                                  //     convertToInternationalizedDateTimeToReadble(
                                  //       val
                                  //     );
                                  //   field.onChange(date);
                                  // }}
                                  //   classNames={{
                                  //     label:
                                  //       "after:content-['*'] after:text-red-500 after:ml-1",
                                  //   }}
                                  //   isInvalid={!!errors?.dateOfBirth}
                                  //   errorMessage={errors?.dateOfBirth?.message}
                                  //   // isDisabled={isDocUpload}
                                  // />
                                  <DateInput
                                    label="Date of Birth"
                                    variant="bordered"
                                    labelPlacement="outside"
                                    // defaultValue={parseDate("2024-04-04")}
                                    description="MM-DD-YYYY"
                                    defaultValue={parseDate(
                                      userData?.dateOfBirth || "1999-12-12"
                                    )}
                                    onChange={(val: CalendarDate) => {
                                      const date =
                                        convertToInternationalizedDateTimeToReadble(
                                          val
                                        );
                                      field.onChange(date);
                                    }}
                                    isDisabled={isDocUpload}
                                  />
                                )}
                              />
                            </div>

                            <div className="">
                              <Controller
                                name="language"
                                control={control}
                                render={({ field, fieldState }) => (
                                  <CustomAutocomplete
                                    label="Language"
                                    isRequired
                                    placeholder="Select a language"
                                    selectedKey={field.value}
                                    defaultItems={languageData}
                                    width="none"
                                    onSelectionChange={(val) => {
                                      field.onChange(val);
                                    }}
                                    isInvalid={!!fieldState.error}
                                    errorMessage={fieldState.error?.message}
                                  />
                                )}
                              />
                            </div>

                            <div className="">
                              <Controller
                                name="currencyCode"
                                control={control}
                                render={({ field, fieldState }) => (
                                  <CustomAutocomplete
                                    label="Currency"
                                    isRequired
                                    placeholder="Search an Currency"
                                    selectedKey={field.value}
                                    defaultItems={currencyCodeData}
                                    width="none"
                                    onSelectionChange={(val) => {
                                      field.onChange(val);
                                    }}
                                    isInvalid={!!fieldState.error}
                                    errorMessage={fieldState.error?.message}
                                  />
                                )}
                              />
                            </div>
                          </div>

                          {/* Bio (Textarea) */}
                          <div className="mt-4">
                            <Textarea
                              label="Bio"
                              isClearable
                              isRequired
                              labelPlacement="outside"
                              placeholder="Tell us about yourself"
                              variant="bordered"
                              value={watch("bio")}
                              onClear={() => console.log("Bio cleared")}
                              {...register("bio")}
                              isInvalid={!!errors?.bio}
                              errorMessage={errors?.bio?.message}
                            />
                          </div>
                        </div>

                        {/* ADDRESS INFORMATION */}
                        <div className="mt-8">
                          <div className="flex flex-initial justify-between items-center">
                            <p className="text-subtitle3">
                              Address Information
                            </p>
                          </div>
                          <Divider className="my-3" />
                          <div className="mt-12 mb-6">
                            <CustomInput
                              isRequired
                              label="Address"
                              radius="sm"
                              variant="bordered"
                              size="md"
                              value={watch("address.addressLine1")}
                              placeholder="Enter your address"
                              type="text"
                              {...register("address.addressLine1")}
                              isInvalid={!!errors?.address?.addressLine1}
                              errorMessage={
                                errors?.address?.addressLine1?.message
                              }
                              isDisabled={isDocUpload}
                            />
                          </div>
                          <div className="grid grid-cols-2 gap-5 mt-2">
                            <Controller
                              name="address.country"
                              control={control}
                              render={({ field, fieldState }) => (
                                <Autocomplete
                                  {...field}
                                  onSelectionChange={(key) =>
                                    field.onChange(key)
                                  }
                                  isRequired
                                  selectedKey={field.value}
                                  radius="sm"
                                  labelPlacement="outside"
                                  size="md"
                                  variant="bordered"
                                  label="Country"
                                  placeholder="Enter your country"
                                  isInvalid={!!fieldState.error}
                                  errorMessage={fieldState.error?.message}
                                  isDisabled={isDocUpload}
                                >
                                  {countryData.map((s) => (
                                    <AutocompleteItem key={s.key}>
                                      {s.label}
                                    </AutocompleteItem>
                                  ))}
                                </Autocomplete>
                              )}
                            />

                            <Controller
                              name="address.state"
                              control={control}
                              render={({ field, fieldState }) => (
                                <Autocomplete
                                  {...field}
                                  onSelectionChange={(key) =>
                                    field.onChange(key)
                                  }
                                  isRequired
                                  selectedKey={field.value}
                                  radius="sm"
                                  labelPlacement="outside"
                                  size="md"
                                  variant="bordered"
                                  label="State"
                                  placeholder="Enter your state"
                                  isInvalid={!!fieldState.error}
                                  errorMessage={fieldState.error?.message}
                                  isDisabled={isDocUpload}
                                >
                                  {canadaProvincesAndTerritories.map((s) => (
                                    <AutocompleteItem key={s.key}>
                                      {s.label}
                                    </AutocompleteItem>
                                  ))}
                                </Autocomplete>
                              )}
                            />

                            <div>
                              <CustomInput
                                radius="sm"
                                isRequired
                                variant="bordered"
                                size="md"
                                label="City"
                                placeholder="Enter your city"
                                type="tel"
                                value={watch("address.city")}
                                {...register("address.city")}
                                isInvalid={!!errors?.address?.city}
                                errorMessage={errors?.address?.city?.message}
                                isDisabled={isDocUpload}
                              />
                            </div>

                            <div>
                              <CustomInput
                                radius="sm"
                                isRequired
                                variant="bordered"
                                size="md"
                                label="Postal Code"
                                placeholder="Enter postal code"
                                type="tel"
                                value={watch("address.postalCode")}
                                {...register("address.postalCode")}
                                isInvalid={!!errors?.address?.postalCode}
                                errorMessage={
                                  errors?.address?.postalCode?.message
                                }
                                isDisabled={isDocUpload}
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-5 "></div>
                        </div>

                        <div className="flex justify-end items-center mt-10 gap-5">
                          <CustomButton
                            type="submit"
                            size="sm"
                            label="Close"
                            color="danger"
                            variant="light"
                            onPress={onClose}
                          />
                          <CustomButton
                            type="submit"
                            size="sm"
                            label="Submit"
                            color="primary"
                            isLoading={loading}
                          />
                        </div>
                      </form>
                    )}
                  </div>
                </Card>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default EditProviderDetails;
