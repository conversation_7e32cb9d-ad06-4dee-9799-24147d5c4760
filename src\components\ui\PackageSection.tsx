import {
  Card,
  Card<PERSON>ody,
  <PERSON><PERSON>eader,
  DatePicker,
  DateRangePicker,
  Divider,
  Radio,
  RadioGroup,
  RangeValue,
  Switch,
} from "@heroui/react";
import {
  CalendarDate,
  getLocalTimeZone,
  parseAbsoluteToLocal,
  parseDate,
  today,
} from "@internationalized/date";
import { memo, useEffect } from "react";
import { Controller, useFieldArray, useFormContext } from "react-hook-form";
import CustomNumberInput from "./CustomNumberInput";
import { FaUser } from "react-icons/fa";
import { BiDollar } from "react-icons/bi";
import CustomDivider from "./CustomDivider";
import CustomButton from "./CustomButton";
import { RiDeleteBin4Line } from "react-icons/ri";
import CustomInput from "./CustomInput";
import { IoIosAddCircleOutline } from "react-icons/io";
import { IServiceSubmitProps } from "../../types";
import {
  convertReadableDateToDateObject,
  convertToInternationalizedDateTimeToReadble,
} from "../../utils/convertTime";
import { MdPercent } from "react-icons/md";
import { BsDashCircle } from "react-icons/bs";
import CustomCheckbox from "./CustomCheckbox";
import PackageIncludes from "./PackageIncludes";
import moment from "moment";

const PackageSection = memo(() => {
  const {
    register,
    watch,
    control,
    setValue,
    trigger,
    formState: { errors },
  } = useFormContext<IServiceSubmitProps>();

  const {
    fields: packaegField,
    append: appendPackage,
    remove: removePackage,
  } = useFieldArray({
    name: "packages",
    control,
  });

  const discountType = watch("discount.discountType");
  const isPackageSelect = watch("isPackage");
  const price = watch("price");
  const valueType = watch("discount.valueType");
  const dd = watch("packages.0.discount.duration");
  const ddd = watch("packages.0.isDiscount");

  console.log("Date< ", ddd);

  // useEffect(() => {
  //   setValue("discount.amount", 0);
  // }, [valueType]);

  useEffect(() => {
    if (packaegField.length === 0)
      appendPackage({
        isSoldOut: false,
        isDiscount: false,
        packageName: "",
        price: 0,
        includes: [],
        discount: {
          discountType: "general-discount",
          valueType: "percentage",
          durationType: "life-time",
          amount: 0,
          duration: {
            start: "",
            end: "",
          },
          maxCount: 0,
        },
      });
  }, [appendPackage, packaegField]);

  return (
    <>
      <div>
        <div>
          <div>
            <div className="flex items-baseline flex-initial gap-4  -mt-2 ">
              <p className="text-body-bold ">Add Packages to Service</p>
              <Controller
                name="isPackage"
                control={control}
                defaultValue={false}
                render={({ field }) => (
                  <Switch
                    isSelected={watch("isPackage")}
                    onValueChange={(val: boolean) => {
                      field.onChange(val);
                      if (val) {
                        setValue("isDiscount", false);
                        if (
                          !watch("packages") ||
                          watch("packages")?.length === 0
                        ) {
                          setValue("packages", [
                            {
                              isSoldOut: false,
                              isDiscount: false,
                              packageName: "",
                              price: 0,
                              includes: [],
                              discount: {
                                discountType: "general-discount",
                                valueType: "percentage",
                                durationType: "life-time",
                                amount: 0,
                                duration: { start: "", end: "" },
                                maxCount: 0,
                              },
                            },
                          ]);
                        }
                      } else {
                        setValue("packages", []);
                      }
                    }}
                    classNames={{
                      wrapper: "bg-blue-100",
                      thumbIcon: "text-blue-500",
                    }}
                    size="sm"
                    className=""
                  />
                )}
              />
            </div>

            {isPackageSelect ? (
              <div className="mt-5">
                {packaegField.map((field, index) => (
                  <div
                    key={field.id}
                    className="grid gap-4 border-1 border-secondary/30  shadow-md bg-gray-50  dark:bg-neutral-800 dark:border-gray-600/50 -m-2 tablet:mt-0 laptop-mt-5 mb-4 px-2  tablet:px-3  pb-5 rounded-md"
                  >
                    <div className="flex justify-between items-center py-4">
                      <Controller
                        name={`packages.${index}.isSoldOut`}
                        control={control}
                        defaultValue={false}
                        render={({ field }) => (
                          <CustomCheckbox
                            label="Mark as Package Sold Out "
                            isSelected={field.value}
                            onValueChange={field.onChange}
                            className="text-body-bold"
                            color="danger"
                          />
                        )}
                      />

                      {packaegField.length > 1 && (
                        <CustomButton
                          label="Remove Package"
                          color="danger"
                          variant="flat"
                          size="sm"
                          isDisabled={!isPackageSelect}
                          onPress={() => removePackage(index)}
                        />
                      )}
                    </div>
                    <div className="grid tablet:grid-cols-2 gap-3  laptop:inline-flex laptop:justify-center laptop:items-center">
                      <CustomInput
                        label="Package Name"
                        type="text"
                        placeholder="Enter name"
                        isDisabled={!isPackageSelect}
                        {...register(`packages.${index}.packageName` as const)}
                        isInvalid={
                          !!errors.packages?.[index]?.packageName?.message
                        }
                        errorMessage={
                          errors.packages?.[index]?.packageName?.message
                        }
                      />
                      <CustomNumberInput
                        label="Price"
                        placeholder="Enter amount"
                        isDisabled={!isPackageSelect}
                        {...register(`packages.${index}.price` as const)}
                        isInvalid={!!errors.packages?.[index]?.price?.message}
                        errorMessage={errors.packages?.[index]?.price?.message}
                      />

                      {packaegField.length > 1 && (
                        <div className="mt-6 cursor-pointer hidden tablet:block">
                          <CustomButton
                            isIconOnly={true}
                            isDisabled={!isPackageSelect}
                            variant="light"
                            onPress={() => removePackage(index)}
                          >
                            <RiDeleteBin4Line
                              size={20}
                              className="text-red-400"
                            />
                          </CustomButton>
                        </div>
                      )}
                    </div>

                    {/* PACKAGE INCLUDE */}
                    <div>
                      <Divider />
                      <p className="text-body-bold mt-3">Package Include</p>
                      {/* INCLUDES */}
                      <div key={field.id} className="rounded-lg space-y-4 mt-4">
                        <PackageIncludes
                          packageIndex={index}
                          isPackageSelect={isPackageSelect}
                        />

                        {/* REMOVE PACKAGE */}
                        {/* <CustomButton
                          label="Remove Package"
                          color="danger"
                          variant="flat"
                          size="sm"
                          className="mt-4"
                          isDisabled={!isPackageSelect}
                          onPress={() => removePackage(index)}
                        /> */}
                      </div>
                    </div>

                    <CustomDivider className="laptop:mb-0 laptop:-my-2" />

                    <div className="flex items-center flex-initial gap-4 ">
                      <p className="text-body-bold">Add Discount </p>
                      <Controller
                        name={`packages.${index}.isDiscount`}
                        control={control}
                        defaultValue={false}
                        render={({ field }) => (
                          <Switch
                            isSelected={watch(
                              `packages.${index}.discount.isDiscount`
                            )}
                            // value={field.value}
                            onValueChange={(val: boolean) => {
                              field.onChange(val);
                              setValue(
                                `packages.${index}.discount.isDiscount`,
                                val
                              );
                              if (!val) {
                                setValue(
                                  `packages.${index}.discount`,
                                  {
                                    discountType: "general-discount",
                                    valueType: "percentage",
                                    durationType: "life-time",
                                    amount: 0,
                                    duration: { start: "", end: "" },
                                    maxCount: 0,
                                  },
                                  { shouldValidate: false, shouldDirty: false }
                                );
                                trigger(`packages.${index}.discount`);
                              }
                            }}
                            isDisabled={!isPackageSelect}
                            size="sm"
                            className=""
                          />
                        )}
                      />
                    </div>

                    <div className="">
                      <div className="grid tablet:grid-cols-2 xl:grid-cols-2 gap-2 tablet:gap-3">
                        {/* DISCOUNT TYPE */}
                        <div className=" border-1 border-secondary/30 rounded-lg p-3 dark:border-gray-600/50 bg-cyan-100 dark:bg-neutral-800">
                          <Controller
                            name={`packages.${index}.discount.valueType`}
                            control={control}
                            defaultValue="percentage"
                            render={({ field, fieldState }) => (
                              <>
                                <RadioGroup
                                  label="Value Type"
                                  orientation="horizontal"
                                  classNames={{
                                    label: "text-body",
                                  }}
                                  value={watch(
                                    `packages.${index}.discount.valueType`
                                  )}
                                  onValueChange={(val: string) => {
                                    field.onChange(val);
                                  }}
                                  isInvalid={!!fieldState.error}
                                  // isDisabled={
                                  //   !watch(`packages.${index}.isDiscount`)
                                  // }
                                  isDisabled={
                                    !watch(
                                      `packages.${index}.discount.isDiscount`
                                    )
                                  }
                                >
                                  <Radio
                                    classNames={{
                                      label: "text-body dark:text-gray-300",
                                    }}
                                    value="amount"
                                    size="sm"
                                    className="mr-3"
                                  >
                                    Amount
                                  </Radio>
                                  <Radio
                                    classNames={{
                                      label: "text-body dark:text-gray-300",
                                    }}
                                    value="percentage"
                                    size="sm"
                                  >
                                    Percentage
                                  </Radio>
                                </RadioGroup>
                              </>
                            )}
                          />
                          {errors.discount?.valueType && (
                            <span className="text-error">
                              {errors.discount?.valueType?.message}
                            </span>
                          )}
                        </div>

                        {/* DISCOUNT DURATION TYPE */}
                        <div className=" border-1 border-secondary/30 rounded-lg p-3 dark:border-gray-600/50 bg-cyan-100  dark:bg-neutral-800">
                          <Controller
                            name={`packages.${index}.discount.durationType`}
                            control={control}
                            defaultValue="life-time"
                            render={({ field, fieldState }) => (
                              <>
                                <RadioGroup
                                  label="Duration Type"
                                  orientation="horizontal"
                                  classNames={{
                                    label: "text-body",
                                  }}
                                  value={watch(
                                    `packages.${index}.discount.durationType`
                                  )}
                                  onValueChange={(val: string) => {
                                    field.onChange(val);
                                  }}
                                  isInvalid={!!fieldState.error}
                                  // isDisabled={
                                  //   !watch(`packages.${index}.isDiscount`)
                                  // }
                                  isDisabled={
                                    !watch(
                                      `packages.${index}.discount.isDiscount`
                                    )
                                  }
                                >
                                  <Radio
                                    classNames={{
                                      label: "text-body dark:text-gray-300",
                                    }}
                                    value="life-time"
                                    size="sm"
                                    className="mr-3"
                                  >
                                    Life time
                                  </Radio>
                                  <Radio
                                    classNames={{
                                      label: "text-body dark:text-gray-300",
                                    }}
                                    value="time-base"
                                    size="sm"
                                  >
                                    Time Base
                                  </Radio>
                                </RadioGroup>
                              </>
                            )}
                          />
                        </div>
                      </div>

                      <div
                        className={`grid ${
                          discountType === "promo-code"
                            ? " 2xl:grid-cols-3"
                            : "2xl:grid-cols-3"
                        }   items-start gap-4 mt-5 laptop:mt-8`}
                      >
                        {/* AMOUNT */}
                        <CustomNumberInput
                          label={
                            watch(`packages.${index}.discount.valueType`) ===
                            "percentage"
                              ? "Percentage"
                              : "Amount"
                          }
                          placeholder="Enter amount"
                          {...register(`packages.${index}.discount.amount`)}
                          isRequireField={true}
                          startContent={
                            watch(`packages.${index}.discount.valueType`) ===
                            "percentage" ? (
                              <MdPercent className="text-gray-400" />
                            ) : (
                              <BiDollar className="text-gray-400" />
                            )
                          }
                          // startContent={<BiDollar className="text-gray-400" />}
                          isInvalid={
                            !!errors?.packages?.[index]?.discount?.amount
                              ?.message
                          }
                          errorMessage={
                            errors?.packages?.[index]?.discount?.amount?.message
                          }
                          // isDisabled={!watch(`packages.${index}.isDiscount`)}
                          isDisabled={
                            !watch(`packages.${index}.discount.isDiscount`)
                          }
                          maxValue={
                            valueType === "percentage" ? 100 : 999999999
                          }
                        />

                        {/* TIME RANGE */}
                        {/* <div> */}
                        {watch(`packages.${index}.discount.durationType`) ===
                        "life-time" ? (
                          <Controller
                            name={`packages.${index}.discount.duration`} // only bind start date
                            control={control}
                            render={({ field }) => (
                              <DatePicker
                                // {...field}
                                label="Start Date"
                                minValue={today(getLocalTimeZone())}
                                variant="bordered"
                                labelPlacement="outside"
                                // value={
                                //   field.value?.start
                                //     ? convertReadableDateToDateObject(
                                //         field.value.start
                                //       )
                                //     : null
                                // }
                                value={
                                  field.value?.start
                                    ? parseDate(field.value.start.split("T")[0]) // "2025-09-24"
                                    : null
                                }
                                // defaultValue={convertReadableDateToDateObject(
                                //   field.value
                                // )}
                                onChange={(value) => {
                                  if (!value) return;
                                  const start =
                                    convertToInternationalizedDateTimeToReadble(
                                      value
                                    );
                                  field.onChange({
                                    start,
                                    end: null,
                                  });
                                  // setValue(
                                  //   `packages.${index}.discount.duration.start`,
                                  //   value
                                  // );
                                }}
                                classNames={{
                                  label:
                                    "after:content-['*'] after:text-red-500 after:ml-1",
                                }}
                                // isDisabled={
                                //   !watch(`packages.${index}.isDiscount`)
                                // }
                                isDisabled={
                                  !watch(
                                    `packages.${index}.discount.isDiscount`
                                  )
                                }
                                isInvalid={
                                  !!errors?.packages?.[index]?.discount
                                    ?.duration?.start
                                }
                                errorMessage={
                                  errors?.packages?.[index]?.discount?.duration
                                    ?.start?.message
                                }
                              />
                            )}
                          />
                        ) : (
                          <Controller
                            name={`packages.${index}.discount.duration`} // full object
                            control={control}
                            render={({ field }) => (
                              <DateRangePicker
                                label="Select Duration"
                                aria-label="Select Duration"
                                pageBehavior="single"
                                visibleMonths={3}
                                minValue={today(getLocalTimeZone())}
                                variant="bordered"
                                value={
                                  field.value?.start && field.value?.end
                                    ? {
                                        start: parseDate(
                                          field.value.start.split("T")[0]
                                        ),
                                        end: parseDate(
                                          field.value.end.split("T")[0]
                                        ),
                                      }
                                    : null
                                }
                                labelPlacement="outside"
                                onChange={(value: any) => {
                                  if (!value?.start || !value?.end) return;
                                  field.onChange({
                                    start:
                                      convertToInternationalizedDateTimeToReadble(
                                        value.start
                                      ),
                                    end: convertToInternationalizedDateTimeToReadble(
                                      value.end
                                    ),
                                  });
                                }}
                                classNames={{
                                  label:
                                    "after:content-['*'] after:text-red-500 after:ml-1",
                                }}
                                // isDisabled={
                                //   !watch(`packages.${index}.isDiscount`)
                                // }
                                isDisabled={
                                  !watch(
                                    `packages.${index}.discount.isDiscount`
                                  )
                                }
                                isInvalid={
                                  !!errors?.packages?.[index]?.discount
                                    ?.duration?.start
                                }
                                errorMessage={
                                  errors?.packages?.[index]?.discount?.duration
                                    ?.start?.message
                                }
                              />
                            )}
                          />
                        )}
                        {/* </div> */}

                        {/* MAX COUNT */}
                        <CustomNumberInput
                          label="Max Count"
                          placeholder="Enter count"
                          {...register(`packages.${index}.discount.maxCount`)}
                          startContent={
                            <FaUser className="text-gray-400" size={14} />
                          }
                          isInvalid={
                            !!errors?.packages?.[index]?.discount?.maxCount
                              ?.message
                          }
                          errorMessage={
                            errors?.packages?.[index]?.discount?.maxCount
                              ?.message
                          }
                          // isDisabled={!watch(`packages.${index}.isDiscount`)}
                          isDisabled={
                            !watch(`packages.${index}.discount.isDiscount`)
                          }
                          description="The maximum number of times this discount can be applied. Once this limit is reached, the discount will automatically expire. To keep it always available, set the value to 0."
                        />
                      </div>

                      {packaegField.length > 1 && (
                        <div className="laptop:hidden mt-5">
                          <CustomButton
                            label="Remove"
                            color="danger"
                            variant="flat"
                            fullWidth={true}
                            isDisabled={!isPackageSelect}
                            onPress={() => removePackage(index)}
                            startContent={
                              <BsDashCircle
                                size={16}
                                className="text-red-600 cursor-pointer"
                              />
                            }
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {packaegField.length < 3 && (
                  <div className="mt-3">
                    <CustomButton
                      label="Add package"
                      isDisabled={!isPackageSelect}
                      startContent={
                        <IoIosAddCircleOutline
                          size={20}
                          className="text-green-600 cursor-pointer"
                        />
                      }
                      variant="flat"
                      color="success"
                      onPress={() =>
                        appendPackage({
                          isSoldOut: false,
                          isDiscount: false,
                          packageName: "",
                          price: 0,
                          includes: {
                            input1: "",
                            input2: "",
                            input3: "",
                            input4: "",
                          },
                          discount: {
                            discountType: "general-discount",
                            valueType: "percentage",
                            durationType: "life-time",
                            amount: 0,
                            duration: {
                              start: "",
                              end: "",
                            },
                            maxCount: 0,
                          },
                        })
                      }
                    />
                  </div>
                )}
              </div>
            ) : (
              <>
                {/* ONLY PRICE */}
                <div className="mt-5 laptop:mt-10">
                  <CustomNumberInput
                    label="Price"
                    description="When you select the package, this price field will be hidden"
                    placeholder="Enter price"
                    {...register("price")}
                    value={price || 0}
                    isRequireField={true}
                    startContent={<BiDollar className="text-gray-400" />}
                    isInvalid={!!errors.price?.message}
                    errorMessage={errors.price?.message}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
});

export default PackageSection;
