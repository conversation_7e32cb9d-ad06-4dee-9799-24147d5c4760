import { addToast } from "@heroui/react";
import {
  CommonSubfolder,
  CustomerSubfolder,
  EntityType,
  ExtSubfolder,
  ProviderSubfolder,
  uploadFileToS3,
} from "./s3FileUploader";

interface Address {
  addressLine1?: string;
  addressLine2?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
}

interface IMultiFileUploadprops {
  files: FileList;
  baseFolder: EntityType;
  mainFolder:
    | CustomerSubfolder
    | ProviderSubfolder
    | CommonSubfolder
    | ExtSubfolder;
  subFolder: string;
  nestedPath?: string;
  errorMessageType?: "File" | "Image" | "Document" | "Video" | "Audio";
  callback: (res: string[]) => void;
}

interface ISingleFileUploadprops {
  file: File | null;
  baseFolder: EntityType;
  mainFolder:
    | CustomerSubfolder
    | ProviderSubfolder
    | CommonSubfolder
    | ExtSubfolder;
  subFolder: string;
  nestedPath?: string;
  errorMessageType?: "File" | "Image" | "Document" | "Video" | "Audio";
  //  callback: (res: string) => void;
}

//MULTIPLE FILE UPLOAD
export const multipleFileUplaodHelper = async ({
  files,
  baseFolder,
  mainFolder,
  nestedPath,
  subFolder,
  errorMessageType = "File",
}: IMultiFileUploadprops) => {
  if (!files || files.length === 0) {
    addToast({
      title: `${errorMessageType} not found`,
      description: `No ${errorMessageType} found to upload. Please try again`,
      radius: "md",
      color: "danger",
    });
    console.error("No files found to upload");
    return [];
  }

  if (!baseFolder || !mainFolder) {
    addToast({
      title: `${errorMessageType} Upload`,
      description:
        "Required parameters are missing or invalid. Please contact support if the issue persists.",
      radius: "md",
      color: "danger",
    });
    console.error(
      "Required parameters are missing or invalid : Base Folder and main folder"
    );
    return [];
  }

  const urlArry: string[] = [];

  for (const file of files) {
    try {
      const { url } = await uploadFileToS3(file, {
        baseFolder: baseFolder,
        mainFolder: mainFolder,
        subFolder: subFolder,
        nestedPath: nestedPath,
        fileName: file.name,
      });
      urlArry.push(url);
    } catch (err) {
      console.error(
        `Failed to upload ${file.name ? file.name : "(File Name is empty)"}:`,
        err
      );
      addToast({
        title: "Upload Failed",
        description: `Could not upload ${file.name ? file.name : "file"}`,
        radius: "md",
        color: "danger",
      });
    }
  }
  return urlArry;
};

//SINGLE FILE UPLOAD
export const singleFileUplaodHelper = async ({
  file,
  baseFolder,
  mainFolder,
  nestedPath,
  subFolder,
  errorMessageType = "File",
}: ISingleFileUploadprops): Promise<string> => {
  if (!file) {
    addToast({
      title: `${errorMessageType} not found`,
      description: `No ${errorMessageType} found to upload. Please try again`,
      radius: "md",
      color: "danger",
    });
    console.error("No files found to upload");
    return "";
  }

  if (!baseFolder || !mainFolder) {
    addToast({
      title: `${errorMessageType} Upload`,
      description:
        "Required parameters are missing or invalid. Please contact support if the issue persists.",
      radius: "md",
      color: "danger",
    });
    console.error(
      "Required parameters are missing or invalid : Base Folder and main folder"
    );
    return "";
  }

  let fileUrl: string = "";

  try {
    const { url } = await uploadFileToS3(file, {
      baseFolder: baseFolder,
      mainFolder: mainFolder,
      subFolder: subFolder,
      nestedPath: nestedPath,
      fileName: file.name,
    });
    fileUrl = url;
  } catch (err) {
    console.error(
      `Failed to upload ${file.name ? file.name : "(File Name is empty)"}:`,
      err
    );
    addToast({
      title: "Upload Failed",
      description: `Could not upload ${file.name ? file.name : "file"}`,
      radius: "md",
      color: "danger",
    });
  }

  return fileUrl;
};

//FIRST LETTER UPPER CASE
export const firstLetterUpperCase = (str: string) => {
  if (!str) return "";
  return str.charAt(0).toUpperCase() + str.slice(1);
};

//ADDRESS FORMAT
export const addressFormatHelper = (addressParts: Address) => {
  const address = [
    addressParts?.postalCode,
    addressParts?.addressLine1,
    addressParts?.addressLine2,
    addressParts?.city,
    stateConvertToShortCode(addressParts?.state || addressParts?.state),
    countryConvertToShortCode(addressParts?.country || addressParts?.country),
  ];

  return address.filter(Boolean).join(", ");
};

const countryConvertToShortCode = (country?: string): string => {
  if (!country) return "";
  const provinces: Record<string, string> = {
    canada: "CA",
  };

  return provinces[country.toLowerCase()] || country;
};

export const stateConvertToShortCode = (state?: string): string => {
  if (!state) return "";
  const provinces: Record<string, string> = {
    alberta: "AB",
    "british columbia": "BC",
    manitoba: "MB",
    "new brunswick": "NB",
    "newfoundland and labrador": "NL",
    "northwest territories": "NT",
    "nova scotia": "NS",
    nunavut: "NU",
    ontario: "ON",
    "prince edward island": "PE",
    quebec: "QC",
    saskatchewan: "SK",
    yukon: "YT",
  };

  return provinces[state.toLowerCase()] || state;
};
