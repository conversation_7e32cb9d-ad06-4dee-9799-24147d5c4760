import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAuth } from "react-oidc-context";

import AppContent from "../components/AppContent";
import Sidebar from "../components/Sidebar";
import { setToken } from "../api/axios/tokenProvider";
import FAQChatbot from "../components/FAQChatbot";

const DefaultLayout = () => {
  const auth = useAuth();
  const location = useLocation();
  const shouldShowChatbot = location.pathname !== "/service/add-service";

  useEffect(() => {
    if (auth.isLoading) return;

    if (auth?.user?.id_token) {
      console.log("Running setToken function...");
      setToken(auth.user.id_token);
    }
  }, [auth, auth.user, auth.isLoading]);

  return (
    // <div className="flex flex-row min-h-screen bg-[linear-gradient(340deg,rgba(255,255,255,1)_30%,rgba(49,118,144,0.5)_100%)] dark:bg-darkModeBackground text-gray-800 dark:text-white ">
    // <div className="flex flex-row min-h-screen bg-[linear-gradient(270deg,rgba(255,255,255,1)_0%,rgba(49,118,144,0.08)_50%)] dark:bg-darkModeBackground text-gray-800 dark:text-white ">
    // <div className="flex flex-row min-h-screen bg-[#F8F8F8] dark:bg-gray-900 text-gray-800 dark:text-white ">
    // <div className="flex flex-row min-h-screen bg-[#FFFCFB] dark:bg-gray-900 text-gray-800 dark:text-white ">
    <div className="flex flex-row min-h-screen bg-[#FEFEFA] dark:bg-gray-900 text-gray-800 dark:text-white ">
      <Sidebar />
      <AppContent />
      {shouldShowChatbot && <FAQChatbot />}
    </div>
  );
};

export default DefaultLayout;
