import { AppError, ErrorCode } from "../../logger/LoggerService";
import { apiClient } from "../axios/apiClient";
import { Path } from "../axios/endpoint";

interface IUserProps {
  id: string;
  data: { documentNo: string; documentType: string; front: File; back: File };
}

export const getUserDetailsById = async (id: string) => {
  console.log("FINAL ID: ", id);
  try {
    const res = await apiClient.get(Path.user + `/${id}`);
    console.log("RES: ", res);
    return res.data;
  } catch (error: any) {
    console.log("Error get user details by id: ", error);
    throw error;
  }
};

export const editUserProfile = async (id: string, data: any) => {
  console.log("FINAL: ", data);
  console.log("FINAL ID: ", id);
  try {
    const res = await apiClient.put(Path.user + `/${id}`, data);
    return res.data;
  } catch (error: any) {
    console.log("Error edit user profile: ", error);
    throw error;
  }
};

export const uploadVerifyDocument = async ({ id, data }: IUserProps) => {
  console.log("FINAL: ", data);
  console.log("FINAL ID: ", id);
  try {
    const res = await apiClient.post(
      Path.uploadVerifyDocument + `/${id}`,
      data,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return res.data;
  } catch (error: any) {
    console.log("Error Upload verify document: ", error);
    throw error;
  }
};

//TEST API
export const testApi = async (data: any) => {
  try {
    const res = await apiClient.get(Path.user + `/${"E-53"}`, data);
    console.log("RES: ", res);
    return res.data;
  } catch (error: any) {
    console.log("Error edit user profile: ", error);
    throw error;
  }
};
