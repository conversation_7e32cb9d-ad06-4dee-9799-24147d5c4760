// import {
//   Modal,
//   ModalBody,
//   ModalContent,
//   Mo<PERSON>Footer,
//   Mo<PERSON>Header,
//   useDisclosure,
// } from "@heroui/react";
// import React from "react";
// import CustomButton from "./CustomButton";

// const VerifyPopupModal = () => {
//   const { isOpen, onOpen, onOpenChange } = useDisclosure(CustomButton);
//   return (
//     <>
//       <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
//         <ModalContent>
//           {(onClose) => (
//             <>
//               <ModalHeader className="flex flex-col gap-1">Verify</ModalHeader>
//               <ModalBody>
//                 <p>
//                   Your account is not verified yet. Please verify your account.
//                 </p>
//               </ModalBody>
//               <ModalFooter>
//                 <CustomButton
//                   label="Close"
//                   color="danger"
//                   variant="light"
//                   onPress={onClose}
//                 />
//                 <CustomButton
//                   label="Verify"
//                   color="danger"
//                   variant="light"
//                   onPress={onClose}
//                 />
//               </ModalFooter>
//             </>
//           )}
//         </ModalContent>
//       </Modal>
//     </>
//   );
// };

// export default VerifyPopupModal;
