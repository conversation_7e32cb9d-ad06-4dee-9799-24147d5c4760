import CustomButton from "../../components/ui/CustomButton";
import ServiceCard from "../../components/ui/ServiceCard";
import { MdAddCircleOutline, MdDiscount } from "react-icons/md";
import { FaList } from "react-icons/fa6";
import { IoGridOutline } from "react-icons/io5";
import CustomInput from "../../components/ui/CustomInput";
import { IoSearchSharp } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import CustomPagination from "../../components/ui/CustomPagination";
import { useMemo, useState } from "react";
import ServiceCardList from "../../components/ui/ServiceCardList";
import {
  useFetchAllService,
  useFetchAllServiceByProvider,
  useFetchServiceDataById,
  useFetchSubscriptions,
} from "../../hooks/queries/useFetchData";
import Loading from "../../components/ui/Loading";
import {
  Mo<PERSON>,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>ontent,
  Mo<PERSON>Footer,
  ModalHeader,
  useDisclosure,
} from "@heroui/react";
import PageHeader from "../../components/ui/PageHeader";
import StatsCard from "../../components/ui/StatsCard";
import TabNav from "../../components/ui/TabNav";
import { FaDollarSign } from "react-icons/fa";
import SmallLoadingSpinner from "../../components/ui/SmallLoadingSpinner";
import NoDataFound from "../NoDataFound";

const AllService = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("all");
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [isListView, setIsListView] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchInput, setSearchInput] = useState<string>("");
  const [searchId, setSearchId] = useState<string>("");

  const { data: userSubs } = useFetchSubscriptions();

  const {
    data: serviceDataById,
    isFetching: isSearching,
    isError: isSearchError,
  } = useFetchServiceDataById(searchId);
  const { data, isLoading } = useFetchAllServiceByProvider(currentPage, 12);

  console.log("serach data: ", serviceDataById);
  console.log("serach searchInput: ", searchInput);
  console.log("searchId searchInput: ", searchId);

  let apiData = [];

  if (serviceDataById?.serviceInfo) {
    apiData = [serviceDataById.serviceInfo];
  } else {
    apiData = data?.services || [];
  }

  const totalPage = useMemo(() => data?.pages || 1, [data]);
  const totalServices = data?.totalActiveServicers ?? 0;

  const allowedServices = userSubs?.plan?.limits?.services ?? 0;
  const isFreePlan = userSubs?.plan?.tier === 0;
  const hasReachedLimit = totalServices >= allowedServices;
  const isDisabled = isFreePlan && hasReachedLimit;
  const shouldShowOverLimitModal = !isFreePlan && hasReachedLimit;

  const tabs = [
    { id: "all", title: "All Service" },
    { id: "active", title: "Active" },
    { id: "inactive", title: "Inactive" },
    { id: "analytics", title: "Analytics" },
  ];

  const handleSearch = () => setSearchId(searchInput);
  const clearSearch = () => setSearchId("");

  return (
    <>
      <PageHeader
        title="My Services"
        description="Manage your service listings"
        components={
          <div className="flex gap-5 mt-2">
            <CustomInput
              isClearable
              size="sm"
              placeholder="Search by service ID"
              type="text"
              className="md:w-[350px]"
              onValueChange={(e) => setSearchInput(e)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSearch();
                }
              }}
              onClear={() => clearSearch()}
            />

            <CustomButton
              label="Search"
              className="ml-2"
              color="secondary"
              onPress={handleSearch}
              isLoading={isSearching}
            />

            <div className="flex justify-end items-center gap-4 mb-8 ">
              <CustomButton
                isIconOnly={true}
                size="sm"
                startContent={<IoGridOutline size={18} />}
                onPress={() => setIsListView(false)}
                className="hidden sm:flex"
              />
              <CustomButton
                isIconOnly={true}
                size="sm"
                startContent={<FaList size={18} />}
                onPress={() => setIsListView(true)}
                className="hidden sm:flex"
              />
              <CustomButton
                label="Add Service"
                type="button"
                size="sm"
                color="primary"
                isDisabled={isDisabled || isLoading}
                startContent={<MdAddCircleOutline size={20} />}
                onPress={() => {
                  if (shouldShowOverLimitModal) {
                    onOpen();
                  } else {
                    navigate("/service/add-service");
                  }
                }}
                className="-mt-6 sm:mt-0"
              />
            </div>
          </div>
        }
      />

      <div className="grid tablet:grid-cols-2 laptop:grid-cols-4 gap-5">
        <StatsCard
          title="Active Service"
          isHoverEffect={true}
          value={28}
          icon={<MdDiscount size={30} className=" text-secondary" />}
        />
        <StatsCard
          title="Active Service"
          isHoverEffect={true}
          value={28}
          icon={<MdDiscount size={30} className=" text-secondary" />}
        />
        <StatsCard
          title="Inactive Service"
          isHoverEffect={true}
          value={"$1200"}
          icon={<FaDollarSign size={30} className=" text-secondary" />}
        />
        {/* <StatsCard
          title="ROI"
          isHoverEffect={true}
          value={"28%"}
          icon={<FaChartLine size={30} className=" text-secondary" />}
        /> */}
      </div>

      <TabNav activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />

      {isLoading || isSearching ? (
        <SmallLoadingSpinner size="md" variant="wave" />
      ) : isSearchError || apiData?.length === 0 ? (
        <NoDataFound isIcon />
      ) : (
        <>
          {/* Content */}
          {isListView ? (
            <>
              <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-col-3 gap-3 mt-5">
                <ServiceCardList data={apiData} />
              </div>
            </>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5 mt-5">
              <ServiceCard data={apiData} />
            </div>
          )}
        </>
      )}

      {!isSearchError ||
        (data?.services?.length === 0 && (
          <div className="flex justify-end items-end py-5 mt-7">
            <CustomPagination
              page={currentPage}
              initialPage={1}
              total={totalPage}
              size="md"
              onChange={setCurrentPage}
              // itemPerPage={viewItemPerPage}
              // onItemPerPageChange={(value) => setViewItemPerPage(value)}
            />
          </div>
        ))}

      {isOpen && (
        <Modal isOpen={isOpen} onClose={onOpenChange}>
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader>Service Limit Exceeded</ModalHeader>
                <ModalBody>
                  <p>
                    You've exceeded your service limit. Adding more services may
                    incur additional charges. Do you want to continue?
                  </p>

                  <ModalFooter>
                    <div className="flex gap-4 mt-4">
                      <CustomButton
                        label="Yes, Continue"
                        color="primary"
                        onPress={() => {
                          onClose();
                          navigate("/user/profile/plan");
                        }}
                        className="btn btn-primary"
                      >
                        Yes, Continue
                      </CustomButton>
                      <CustomButton
                        label="Cancel"
                        color="danger"
                        onPress={() => onClose()}
                        className="btn btn-secondary"
                      >
                        Cancel
                      </CustomButton>
                    </div>
                  </ModalFooter>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default AllService;
