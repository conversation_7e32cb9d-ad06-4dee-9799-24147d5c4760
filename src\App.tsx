import React, { Suspense, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Routes, Route, useNavigate } from "react-router-dom";
import routes from "./routes.tsx";
import { useAuth } from "react-oidc-context";
import ROLE from "./Role.tsx";
import Loading from "./components/ui/Loading.tsx";
// import UnauthorizedPage from "./pages/UnauthorizedPage.tsx";
// import LoginInCallback from "./pages/auth/LoginInCallback.tsx";
// import LogoutRedirect from "./pages/auth/LogoutRedirect.tsx";
// import Error404Page from "./pages/Error404Page.tsx";
import "quill/dist/quill.snow.css";
import PasswordReset from "./pages/auth/PasswordReset.tsx";
import ConfirmCodePage from "./pages/auth/ConfirmCodePage.tsx";
import { useFetchUserDetailsById } from "./hooks/queries/useFetchData.ts";
import {
  <PERSON><PERSON>,
  <PERSON>dal,
  Modal<PERSON>ody,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  useDisclosure,
} from "@heroui/react";
import CustomButton from "./components/ui/CustomButton.tsx";
import { AiOutlineFileProtect } from "react-icons/ai";
import { useAppStore } from "./pages/context/AppStore.tsx";
import ServiceUnavailable from "./pages/error/ServiceUnavailable.tsx";

const DefaultLayout = React.lazy(() => import("./layout/DefaultLayout"));
const Error404Page = React.lazy(() => import("./pages/Error404Page.tsx"));
const UnauthorizedPage = React.lazy(
  () => import("./pages/UnauthorizedPage.tsx")
);
const LoginInCallback = React.lazy(
  () => import("./pages/auth/LoginInCallback.tsx")
);
const LogoutRedirect = React.lazy(
  () => import("./pages/auth/LogoutRedirect.tsx")
);

const App = () => {
  const auth = useAuth();
  const { isDown } = useAppStore();

  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const isAuthenticated = auth?.isAuthenticated || false;

  const { data, refetch } = useFetchUserDetailsById(
    auth.user?.profile?.preferred_username || ""
  );
  console.log("isDown: ", isDown);
  useEffect(() => {
    if (!auth?.isAuthenticated) return;
    // TODO: Chnage status after isDocumentComplete
    refetch().then(() => {
      const userData = data?.user;
      if (userData && userData.IsDocUploaded) return;
      if (userData && userData.ISVerified === false) {
        onOpen();
      }
    });
  }, [auth?.isAuthenticated, data, refetch, onOpen]);

  useEffect(() => {
    if (auth?.isAuthenticated) {
      const roles = auth?.user?.profile?.["cognito:groups"] as string[];
      if (roles) {
        const provider = roles?.includes(ROLE.PROVIDER);
        const customer = roles?.includes(ROLE.CUSTOMER);

        sessionStorage.setItem("hasP-rl", String(provider || "false"));
        sessionStorage.setItem("hasC-rl", String(customer || "false"));
        sessionStorage.setItem("isAuth", "true");
      }
    } else {
      sessionStorage.setItem("isAuth", "false");
    }
  }, [auth?.isAuthenticated, auth?.user?.profile]);

  if (auth.isLoading) {
    return <Loading label="Loading..." />;
  }

  if (!isAuthenticated) {
    auth.signinRedirect();
    return null;
  }

  // if (!isDown) return null;

  // return (
  //   <div className="bg-red-500 text-white p-3 text-center">
  //     Service is temporarily unavailable. Please try again later.
  //   </div>
  // );

  localStorage.setItem("isAuthenticated", JSON.stringify(isAuthenticated));

  return (
    <>
      <BrowserRouter>
        <Suspense fallback={<Loading label="Loading..." />}>
          <Routes>
            {/* {isDown ? (
              <Route path="*" element={<ServiceUnavailable />} />
            ) : ( */}
            <>
              <Route path="/" element={<DefaultLayout />}>
                {routes.map(({ path, element }, index) => (
                  <Route
                    key={index}
                    path={path}
                    element={
                      <Suspense fallback={<Loading label="Loading..." />}>
                        {element}
                      </Suspense>
                    }
                  />
                ))}
              </Route>
              <Route path="/unauthorized" element={<UnauthorizedPage />} />
              <Route path="/callback-signin" element={<LoginInCallback />} />
              <Route path="/logout/redirect" element={<LogoutRedirect />} />
              <Route path="/reset-password" element={<PasswordReset />} />
              <Route path="/confirm-code" element={<ConfirmCodePage />} />
              <Route path="*" element={<Error404Page />} />
            </>
            {/* )} */}
          </Routes>
        </Suspense>
      </BrowserRouter>

      {/* Verification Modal */}

      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent className="p-6">
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex flex-col justify-center items-center">
                  <div className="mb-5">
                    <AiOutlineFileProtect
                      size={60}
                      className="text-green-600"
                    />
                  </div>
                  <p className="text-heading3">Verify your account!</p>
                </div>
              </ModalHeader>
              <ModalBody className="flex flex-col gap-2 items-center justify-center">
                <p className="text-center">
                  Your account is not verified yet. Please verify your account
                  to access all features.
                </p>
                <div className="flex gap-4 mt-4 items-center justify-center">
                  <CustomButton
                    label="Got to verify"
                    color="primary"
                    size="md"
                    onPress={() => {
                      onClose();
                      // navigate("account/profile/profile-setting");
                    }}
                    className="btn btn-primary"
                  ></CustomButton>
                  <CustomButton
                    label="Cancel"
                    color="danger"
                    size="md"
                    onPress={() => onClose()}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </CustomButton>
                </div>
              </ModalBody>
              {/* <ModalFooter className="flex"></ModalFooter> */}
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default App;
