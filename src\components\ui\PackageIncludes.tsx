// PackageIncludes.tsx
import { useFieldArray, useFormContext } from "react-hook-form";
import { IoIosAddCircleOutline } from "react-icons/io";
import { RiDeleteBin4Line } from "react-icons/ri";
import CustomInput from "./CustomInput";
import CustomButton from "./CustomButton";
import { useEffect } from "react";

const PackageIncludes = ({
  packageIndex,
  isPackageSelect,
}: {
  packageIndex: number;
  isPackageSelect: boolean;
}) => {
  const {
    control,
    register,
    formState: { errors },
  } = useFormContext();

  const { fields, append, remove } = useFieldArray({
    name: `packages.${packageIndex}.includes`,
    control,
  });

  useEffect(() => {
    if (fields.length === 0) append("");

    if (fields.length === 0) append({ value: "" });
  }, [append, fields.length]);

  return (
    <div>
      <div className="space-y-3">
        {fields.map((field, incIndex) => (
          <div key={field.id} className="flex items-center gap-2">
            <CustomInput
              type="text"
              placeholder="Enter package include"
              isDisabled={!isPackageSelect}
              {...register(
                `packages.${packageIndex}.includes.${incIndex}.value` as const
              )}
              isInvalid={
                !!errors.packages?.[packageIndex]?.includes?.[incIndex]?.value
                  ?.message
              }
              errorMessage={
                errors.packages?.[packageIndex]?.includes?.[incIndex]?.value
                  ?.message
              }
            />

            {fields.length > 1 && (
              <CustomButton
                isIconOnly
                color="danger"
                variant="faded"
                size="sm"
                isDisabled={!isPackageSelect}
                onPress={() => remove(incIndex)}
              >
                <RiDeleteBin4Line size={18} />
              </CustomButton>
            )}
          </div>
        ))}
      </div>
      <CustomButton
        label="Add Include"
        color="success"
        variant="flat"
        size="sm"
        className="mt-3"
        isDisabled={!isPackageSelect}
        startContent={<IoIosAddCircleOutline size={18} />}
        onPress={() => append({ value: "" })}
      />
    </div>
  );
};

export default PackageIncludes;
