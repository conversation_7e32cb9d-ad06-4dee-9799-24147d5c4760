import {
  Controller,
  FormProvider,
  useFieldArray,
  useForm,
} from "react-hook-form";
import { defaultServiceValues } from "../../utils/defaultValue";
import CustomButton from "../../components/ui/CustomButton";
import { BiDollar } from "react-icons/bi";
import {
  addToast,
  Alert,
  Avatar,
  Card,
  CardBody,
  CardHeader,
  DatePicker,
  DateRangePicker,
  Divider,
  Radio,
  RadioGroup,
  RangeValue,
  Select,
  SelectedItems,
  Selection,
  SelectItem,
  Switch,
} from "@heroui/react";
import CustomInput from "../../components/ui/CustomInput";
import {
  useFetchCategory,
  useFetchStaff,
  useFetchSubCategory,
  useFetchUserDetailsById,
} from "../../hooks/queries/useFetchData";
import CustomAutocomplete from "../../components/ui/CustomAutocomplete";
import {
  convertStaffStringToArray,
  createSlug,
  formatCommaSeperateTextToArray,
  formateDataForDropdown,
  processAdditionalServices,
  processAvailabilityNew,
  processDiscount,
  processPackage,
} from "../../utils/serviceUtils";
import { useEffect, useState } from "react";
import {
  IAdditionalServicesResponse,
  IDiscountProps,
  IFaqProps,
  IPackageProps,
  IServiceProps,
  IServiceSubmitProps,
  iStaffGetProps,
  ISubcategoryProps,
} from "../../types";
import CustomNumberInput from "../../components/ui/CustomNumberInput";
import CustomChip from "../../components/ui/CustomChip";
import { IoIosAddCircleOutline, IoMdClose } from "react-icons/io";
import { RiDeleteBin4Line } from "react-icons/ri";
import TextEditor from "../../components/ui/TextEdior";
import GallaryInput from "../../components/ui/GallaryInput";
import { BsDashCircle, BsLink45Deg } from "react-icons/bs";
import CustomCheckbox from "../../components/ui/CustomCheckbox";
import { convertToInternationalizedDateTimeToReadble } from "../../utils/convertTime";
import { yupResolver } from "@hookform/resolvers/yup";
import { serviceSchema } from "../../validation/serviceSchema";
import CustomDivider from "../../components/ui/CustomDivider";
import { useAuth } from "react-oidc-context";
import { multipleFileUplaodHelper } from "../../utils/common";
import { useSumbitServiceMutation } from "../../hooks/mutations/usePostData";
import FullLoading from "../../components/ui/FullLoading";

import { CalendarDate, getLocalTimeZone, today } from "@internationalized/date";
import { FaUser } from "react-icons/fa";
import PackageSection from "../../components/ui/PackageSection";
import ServicePreview from "./ServicePreview";
import { MdPercent } from "react-icons/md";
import AvailabilitySection from "../../components/ui/AvailabilitySection";
import LocationInputs from "../../components/LocationInputs";
import SeoSection from "../../components/ui/SeoSection";
import { useNavigate } from "react-router-dom";
import CustomCardWithHeader from "../../components/ui/CustomCardWithHeader";
import PageHeader from "../../components/ui/PageHeader";

interface IDropdownData {
  label: string;
  id: string;
}

const AddService1 = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const providerId = auth.user?.profile?.preferred_username;

  const [isLoading, setIsLoading] = useState(false);

  const [categoryList, setCategoryList] = useState<IDropdownData[]>([]);
  const [subcategoryList, setSubcategoryList] = useState<IDropdownData[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<Selection>(new Set([]));

  const [isAddtional, setIsAddtional] = useState(false);
  const [isFaq, setIsFaq] = useState(false);
  const [isPackage, setIsPackage] = useState(false);

  //API DATA
  const { data: apiCategoryData } = useFetchCategory();
  const { data: apiSubCategoryData } = useFetchSubCategory();
  const { data: apiStaffData } = useFetchStaff({ page: 1, limit: 100 });
  const { data: providerDetails } = useFetchUserDetailsById(providerId);
  const { mutateAsync } = useSumbitServiceMutation();

  const form = useForm<IServiceSubmitProps>({
    defaultValues: defaultServiceValues,
    shouldUnregister: true,
    resolver: yupResolver(serviceSchema),
    mode: "onChange",
    reValidateMode: "onChange",
    context: { isFaq, isAddtional, isPackage },
  });

  const {
    register,
    control,
    setValue,
    resetField,
    watch,
    reset,
    trigger,
    clearErrors,
    formState: { errors },
  } = form;

  const categoryId = watch("categoryId");
  const discountType = watch("discount.discountType");
  const discountDurationType = watch("discount.durationType");
  const discountValueType = watch("discount.valueType");
  const isPackageSelect = watch("isPackage");
  const serviceTitle = watch("serviceTitle");
  const fullData = watch();

  console.log("VALIDATION ERROR: ", errors);
  console.log("PAYLOD fullData: ", fullData);

  // useEffect(() => {
  //   const generateSlug = async () => {
  //     if (serviceTitle) {
  //       const data = await createSlug(serviceTitle);
  //       setValue("slug", data);
  //       console.log("Generated slug: ", data);
  //     }
  //   };

  //   generateSlug();
  // }, [serviceTitle, setValue]);

  useEffect(() => {
    setIsPackage(isPackageSelect);
  }, [isPackageSelect]);

  //Category formating for display value in dropdown
  useEffect(() => {
    const formtedCategory = formateDataForDropdown(
      apiCategoryData?.categories,
      "categoryName",
      "categoryId"
    );
    if (!formtedCategory) return;
    setCategoryList(formtedCategory);
  }, [apiCategoryData?.categories]);

  //SubCategory formating for display value in dropdown
  useEffect(() => {
    const getSubcategorybyCategoryId = apiSubCategoryData?.subCategories.filter(
      (item: ISubcategoryProps) => {
        return item.categoryId === categoryId;
      }
    );
    if (!getSubcategorybyCategoryId) return;

    const formtedSubCategory = formateDataForDropdown(
      getSubcategorybyCategoryId,
      "subCategoryName",
      "subCategoryId"
    );

    setSubcategoryList(formtedSubCategory);
  }, [categoryId, apiSubCategoryData?.subCategories, setValue]);

  //include
  const { fields, append, remove } = useFieldArray({
    name: "includes",
    control,
  });

  //addtional service
  const {
    fields: addtionalServiceField,
    append: appendAddtionalService,
    remove: removeAdditionalService,
  } = useFieldArray({
    name: "additionalService",
    control,
  });

  //faq
  const {
    fields: faqField,
    append: appendFaq,
    remove: removeFaq,
  } = useFieldArray({
    name: "faq",
    control,
  });

  useEffect(() => {
    if (fields.length === 0) append("");

    if (addtionalServiceField.length === 0)
      appendAddtionalService({
        serviceItem: "",
        price: 0,
        id: "",
        images: "",
      });
  }, [append, fields.length]);

  useEffect(() => {
    setValue("isfaq", isFaq);
    if (!isFaq) {
      resetField("faq");
    }
  }, [isFaq, resetField, setValue]);

  useEffect(() => {
    setValue("isAddtional", isAddtional);
    if (!isAddtional) {
      resetField("additionalService");
    }
  }, [isAddtional, resetField, setValue]);

  //SUBMIT DATA
  const onSubmit = async (data: IServiceSubmitProps) => {
    console.log(data);
    setIsLoading(true);
    try {
      let staffArray = [];
      let formatSeoKeyword = [];
      let formatFaqs: IFaqProps[] | [] = [];
      let processPackageData: IPackageProps[] = [];
      let processDiscountData: IDiscountProps | null = null;
      let addtionalServiceResult: IAdditionalServicesResponse[] = [];

      const files = data.gallery?.[0]?.serviceImages;
      const addtionalService = data?.additionalService;
      const user = providerDetails?.user;
      const nestedFilePath = `${data?.serviceTitle}-${Date.now()}`;
      const generateProviderFolder = `${user.userId}-${user.name}`;
      const staff = data?.staff;
      const availability = data?.availability;
      const packageData = data?.packages;
      const isPackageData = data?.isPackage;
      const isDiscountHas = data?.isDiscount;
      const discountData = data?.discount;
      const selectFaq = data?.faq;
      const selectIncludes = data?.includes;
      const seoTitle = data?.seo[0]?.metaTitle || "";
      const seoKeyword = data?.seo[0]?.metaKeywords || "";
      const seoDescription = data?.seo[0]?.metaDescription || "";

      //Image upload
      if (!user || !data?.serviceTitle || !generateProviderFolder) {
        addToast({
          title: "Configuration Error",
          description:
            "Upload configuration parameters are missing. Please contact support if the issue persists.",
          radius: "md",
          color: "danger",
        });
        setIsLoading(false);
        return;
      }

      const galleryImageUrls = await multipleFileUplaodHelper({
        files,
        baseFolder: "provider",
        mainFolder: "service",
        subFolder: generateProviderFolder,
        nestedPath: nestedFilePath,
        errorMessageType: "Image",
      });

      if (!galleryImageUrls || galleryImageUrls.length === 0) {
        addToast({
          title: "Image upload",
          description:
            "Uplaod failed. Please contact support if the issue persists.",
          radius: "md",
          color: "danger",
        });
        setIsLoading(false);
        return;
      }

      console.log("Step - 1");
      //addtional service image upload
      if (
        Array.isArray(addtionalService) &&
        addtionalService.length > 0 &&
        addtionalService[0]?.images?.length > 0
      ) {
        addtionalServiceResult = await processAdditionalServices(
          addtionalService,
          generateProviderFolder,
          nestedFilePath
        );
      } else {
        addtionalServiceResult = [];
      }

      console.log("Step - 2");

      const generatSlug = await createSlug(data?.serviceTitle);
      if (!generatSlug) {
        addToast({
          title: "Slug generation",
          description: "Slug generation failed. try again.",
          radius: "md",
          color: "danger",
        });
        setIsLoading(false);
        return;
      }
      console.log("Step - 3");
      if (isPackageData) {
        processPackageData = await processPackage(packageData);
        console.log("PKG: ", processPackageData);
      }
      console.log("Step - 4");
      if (isDiscountHas && !isPackage) {
        processDiscountData = await processDiscount(discountData);
      }

      staffArray = await convertStaffStringToArray(staff);
      console.log("Step - 5");
      if (staffArray.length === 0) {
        staffArray.push(user.userId);
      }
      console.log("Step - 6");
      const processAvilabilityResult = await processAvailabilityNew(
        availability
      );
      console.log("Step - 7");
      if (selectFaq && selectFaq.length > 0) {
        if (
          (selectFaq[0]?.answer ?? "").trim() === "" &&
          (selectFaq[0]?.question ?? "").trim() === ""
        ) {
          formatFaqs = [];
        } else {
          formatFaqs = selectFaq;
        }
      }
      console.log("Step - 8");
      console.log("INCLUDE: ", selectIncludes);
      const formatIncludes = Array.isArray(selectIncludes)
        ? selectIncludes.filter((item) => item && item.trim() !== "")
        : [];

      console.log("Step - 9");
      console.log("formatIncludes ", formatIncludes);

      formatSeoKeyword = await formatCommaSeperateTextToArray(seoKeyword);

      const payload = {
        ...data,
        slug: generatSlug,
        price: data?.isPackage ? null : Number(data.price),
        staff: staffArray,
        additionalService: addtionalServiceResult,
        gallery: [
          {
            serviceImages: galleryImageUrls,
            videoLink: data?.gallery[0]?.videoLink,
          },
        ],
        availability: processAvilabilityResult,
        packages: processPackageData,
        discount: processDiscountData,
        faq: formatFaqs,
        includes: formatIncludes,
        seo: [
          {
            metaTitle: seoTitle,
            metaKeywords: formatSeoKeyword,
            metaDescription: seoDescription,
          },
        ],
      };

      console.log("Final Service Create payload: ", payload);

      // await mutateAsync(payload);
      // reset();
      // clearErrors();
    } catch (e: unknown) {
      console.log("");
    } finally {
      setIsLoading(false);
    }
  };

  const generateSlug = async () => {
    const data = await createSlug(watch("serviceTitle"));
    setValue("slug", data);
    console.log("data: ", data);
  };

  useEffect(() => {
    generateSlug();
  }, [watch("serviceTitle")]);

  const navigateToVerify = () => {
    navigate("/account/profile/profile-setting");
  };

  return (
    <>
      {isLoading && (
        <FullLoading label="Please wait, this may take some time. Submitting..." />
      )}

      {/* <Alert
        title="Account not verified"
        description=" Your account is not verified yet. Please verify your account to access all features."
        color="warning"
        endContent={
          <CustomButton
            label="Verify Now"
            color="warning"
            variant="flat"
            onPress={navigateToVerify}
          />
        }
      /> */}

      <PageHeader
        title="Add Service"
        description="Add a new service to your portfolio, and start earning today."
        components={
          <div className="flex gap-5">
            <div className="flex flex-initial justify-end items-center">
              <ServicePreview data={fullData} category={apiCategoryData} />
            </div>
          </div>
        }
      />

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 lg:grid-cols-2  laptop:gap-2 -mx-2 laptop:mx-0  ">
            {/* FIRST COL */}
            <div>
              <CustomCardWithHeader
                title="Basic Information"
                subtitle="Fill in the basic information of your service"
                isHeaderDevider
                className="mr-5"
                mainContent={
                  <div>
                    <div className="gap-3 tablet:gap-3 -mt-2 ">
                      <CustomInput
                        label="Service Title"
                        type="text"
                        isRequireField={true}
                        placeholder="Enter title"
                        isInvalid={!!errors?.serviceTitle}
                        errorMessage={errors?.serviceTitle?.message}
                        {...register("serviceTitle")}
                      />

                      {/* Slug */}
                      {/* <CustomInput
                    label="Service Slug"
                    type="text"
                    isRequired
                    placeholder="Enter slug"
                    // value={generateSlug()}
                    isInvalid={!!errors?.slug}
                    errorMessage={errors?.slug?.message}
                    {...register("slug")}
                  /> */}
                      <div className="grid tablet:grid-cols-2 gap-4 tablet:gap-4  mt-4">
                        <div>
                          <Controller
                            name="categoryId"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <>
                                <CustomAutocomplete
                                  label="Category"
                                  placeholder="Select category"
                                  defaultItems={categoryList}
                                  width="none"
                                  onSelectionChange={(val) => {
                                    field.onChange(val);
                                  }}
                                  isInvalid={!!errors.categoryId?.message}
                                  errorMessage={errors.categoryId?.message}
                                />
                              </>
                            )}
                          />
                        </div>
                        <div>
                          <Controller
                            name="subCategoryId"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <>
                                <CustomAutocomplete
                                  label="Sub Category"
                                  placeholder="Select sub category"
                                  defaultItems={subcategoryList}
                                  width="none"
                                  onSelectionChange={(val) => {
                                    field.onChange(val);
                                  }}
                                  description="First select a category"
                                  isInvalid={!!errors.subCategoryId?.message}
                                  errorMessage={errors.subCategoryId?.message}
                                />
                              </>
                            )}
                          />
                        </div>
                      </div>

                      {/* STAFF */}
                      <Controller
                        name="staff"
                        defaultValue={[]}
                        control={control}
                        render={({ field, fieldState }) => (
                          <Select
                            {...field}
                            variant="bordered"
                            label="Add Staff"
                            labelPlacement="outside"
                            placeholder="Select a service"
                            selectionMode="multiple"
                            selectedKeys={selectedStaff}
                            onSelectionChange={setSelectedStaff}
                            items={apiStaffData?.staff || []}
                            errorMessage={fieldState.error?.message}
                            renderValue={(
                              items: SelectedItems<iStaffGetProps>
                            ) => (
                              <div className="flex flex-wrap gap-2">
                                {items.map((item) => (
                                  <CustomChip
                                    key={item.key}
                                    label={item?.data?.fullName}
                                  />
                                ))}
                              </div>
                            )}
                          >
                            {(data: iStaffGetProps) => (
                              <SelectItem
                                key={data.staffId}
                                textValue={data.fullName}
                              >
                                <div className="flex gap-2 items-center">
                                  <Avatar
                                    alt={data.fullName}
                                    className="shrink-0"
                                    size="sm"
                                  />
                                  <div className="flex flex-col">
                                    <span className="text-small">
                                      {data.fullName}
                                    </span>
                                    <span className="text-tiny text-default-400">
                                      {data.staffId}
                                    </span>
                                  </div>
                                </div>
                              </SelectItem>
                            )}
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                }
              />

              {/* PRICE AND PACKAGE*/}
              <CustomCardWithHeader
                title="Price & Package"
                subtitle="Add your service price and package"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <>
                    <PackageSection />
                  </>
                }
              />

              {/* DISCOUNT */}
              {!isPackageSelect && (
                <CustomCardWithHeader
                  title="Discount"
                  subtitle="Add your service discount"
                  isHeaderDevider
                  className="mt-5 mr-5"
                  mainContent={
                    <>
                      <div className="-mt-2">
                        <div className="flex items-baseline flex-initial gap-4 ">
                          <p className="text-body-bold ">Enable Discount</p>
                          <Controller
                            name="discount.isDiscount"
                            control={control}
                            defaultValue={false}
                            render={({ field }) => (
                              <Switch
                                isSelected={watch("discount.isDiscount")}
                                onValueChange={(val: boolean) => {
                                  field.onChange(val);
                                  setValue("isDiscount", val);
                                  if (!val) {
                                    console.log("RESET----------------");
                                    resetField("discount");
                                    // resetField("discount.amount");
                                    setValue("discount.amount", 0);
                                    trigger("discount.isDiscount");
                                  }
                                  trigger("discount.isDiscount");
                                }}
                                size="sm"
                                className="mb-5"
                              />
                            )}
                          />
                        </div>
                        <div className="grid tablet:grid-cols-2 laptop:grid-cols-3 gap-3">
                          <div className="flex flex-col ">
                            {/* DISCOUNT DURATION TYPE */}
                            <div
                              className={`${
                                errors.discount?.discountType
                                  ? "border-red-500 bg-red-50 dark:border-red-500"
                                  : ""
                              } border-1  border-secondary/30 rounded-lg p-3 dark:border-gray-600/50  bg-cyan-100 dark:bg-neutral-800`}
                            >
                              <Controller
                                name="discount.discountType"
                                control={control}
                                defaultValue="general-discount"
                                render={({ field }) => (
                                  <>
                                    <RadioGroup
                                      label="Discount Type"
                                      orientation="horizontal"
                                      classNames={{
                                        label: "text-body",
                                      }}
                                      defaultValue="general-discount"
                                      value={watch("discount.discountType")}
                                      onValueChange={(val: string) => {
                                        field.onChange(val);
                                        if (val === "promo-code") {
                                          resetField("discount.promoCode");
                                        }
                                      }}
                                      isInvalid={
                                        !!errors.discount?.discountType
                                      }
                                      isDisabled={!watch("discount.isDiscount")}
                                    >
                                      <Radio
                                        classNames={{
                                          label: "text-sm dark:text-gray-300",
                                        }}
                                        value="general-discount"
                                        size="sm"
                                        className="mr-3"
                                      >
                                        General Discount
                                      </Radio>
                                      <Radio
                                        classNames={{
                                          label: "text-sm dark:text-gray-300",
                                        }}
                                        value="promo-code"
                                        size="sm"
                                      >
                                        Promo Code
                                      </Radio>
                                    </RadioGroup>
                                  </>
                                )}
                              />
                            </div>
                            {errors.discount?.discountType && (
                              <span className="text-error">
                                {errors.discount?.discountType.message}
                              </span>
                            )}
                          </div>

                          {/* DISCOUNT TYPE */}
                          <div className=" border-1 border-secondary/30 rounded-lg p-3 dark:border-gray-600/50 bg-cyan-100 dark:bg-neutral-800">
                            <Controller
                              name="discount.valueType"
                              control={control}
                              defaultValue="percentage"
                              render={({ field }) => (
                                <>
                                  <RadioGroup
                                    label="Value Type"
                                    orientation="horizontal"
                                    classNames={{
                                      label: "text-body",
                                    }}
                                    value={watch("discount.valueType")}
                                    onValueChange={(val: string) => {
                                      field.onChange(val);
                                    }}
                                    isInvalid={!!errors.discount?.valueType}
                                    isDisabled={!watch("discount.isDiscount")}
                                  >
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="amount"
                                      size="sm"
                                      className="mr-3"
                                    >
                                      Amount
                                    </Radio>
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="percentage"
                                      size="sm"
                                    >
                                      Percentage
                                    </Radio>
                                  </RadioGroup>
                                </>
                              )}
                            />
                            {errors.discount?.valueType && (
                              <span className="text-error">
                                {errors.discount?.valueType?.message}
                              </span>
                            )}
                          </div>

                          {/* DISCOUNT DURATION TYPE */}
                          <div className=" border-1 border-secondary/30 rounded-lg p-3 dark:border-gray-600/50 bg-cyan-100  dark:bg-neutral-800">
                            <Controller
                              name="discount.durationType"
                              control={control}
                              defaultValue="life-time"
                              render={({ field }) => (
                                <>
                                  <RadioGroup
                                    label="Duration Type"
                                    orientation="horizontal"
                                    classNames={{
                                      label: "text-body",
                                    }}
                                    value={watch("discount.durationType")}
                                    onValueChange={(val: string) => {
                                      field.onChange(val);
                                    }}
                                    isInvalid={!!errors.discount?.durationType}
                                    isDisabled={!watch("discount.isDiscount")}
                                  >
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="life-time"
                                      size="sm"
                                      className="mr-3"
                                    >
                                      Life time
                                    </Radio>
                                    <Radio
                                      classNames={{
                                        label: "text-sm dark:text-gray-300",
                                      }}
                                      value="time-base"
                                      size="sm"
                                    >
                                      Time Base
                                    </Radio>
                                  </RadioGroup>
                                </>
                              )}
                            />
                          </div>
                        </div>

                        <div
                          className={`grid ${
                            discountType === "promo-code"
                              ? " 2xl:grid-cols-3"
                              : "2xl:grid-cols-2"
                          }  items-start gap-4 mt-8`}
                        >
                          {/* AMOUNT */}
                          {discountType === "promo-code" && (
                            <CustomInput
                              label="Code"
                              placeholder="Type code"
                              isRequireField={true}
                              {...register("discount.promoCode")}
                              type="text"
                              value={watch("discount.promoCode")?.toUpperCase()}
                              isInvalid={!!errors?.discount?.promoCode}
                              errorMessage={
                                errors?.discount?.promoCode?.message
                              }
                              isDisabled={!watch("discount.isDiscount")}
                            />
                          )}

                          {/* AMOUNT */}
                          <div className="">
                            <CustomNumberInput
                              label={
                                discountValueType === "percentage"
                                  ? "Percentage"
                                  : "Amount"
                              }
                              placeholder="Enter amount"
                              {...register("discount.amount")}
                              isRequireField={true}
                              startContent={
                                discountValueType === "percentage" ? (
                                  <MdPercent className="text-gray-400" />
                                ) : (
                                  <BiDollar className="text-gray-400" />
                                )
                              }
                              isInvalid={!!errors.discount?.amount?.message}
                              errorMessage={errors.discount?.amount?.message}
                              isDisabled={!watch("discount.isDiscount")}
                            />
                          </div>

                          {/* TIME RANGE */}
                          <div
                          // className={`w-full -mt-5 ${
                          //   discountType === "promo-code"
                          //     ? "2xl:col-span-2"
                          //     : ""
                          // }`}
                          >
                            {discountDurationType === "life-time" ? (
                              <Controller
                                name={"discount.duration"}
                                control={control}
                                render={({ field }) => (
                                  <DatePicker
                                    label="Start Date"
                                    aria-label="Select Start date"
                                    minValue={today(getLocalTimeZone())}
                                    variant="bordered"
                                    labelPlacement="outside"
                                    onChange={(value) => {
                                      if (!value) return;
                                      const start =
                                        convertToInternationalizedDateTimeToReadble(
                                          value
                                        );
                                      field.onChange({
                                        start,
                                        end: null,
                                      });
                                    }}
                                    classNames={{
                                      label:
                                        "after:content-['*'] after:text-red-500 after:ml-1",
                                    }}
                                    isDisabled={!watch("discount.isDiscount")}
                                    isInvalid={
                                      !!errors?.discount?.duration?.start
                                    }
                                    errorMessage={
                                      errors?.discount?.duration?.start?.message
                                    }
                                  />
                                )}
                              />
                            ) : (
                              <Controller
                                name={"discount.duration"}
                                control={control}
                                render={({ field }) => (
                                  <DateRangePicker
                                    label="Select  Duration"
                                    pageBehavior="single"
                                    visibleMonths={3}
                                    minValue={today(getLocalTimeZone())}
                                    variant="bordered"
                                    labelPlacement="outside"
                                    onChange={(value: any) => {
                                      if (!value?.start || !value?.end) return;
                                      field.onChange({
                                        start:
                                          convertToInternationalizedDateTimeToReadble(
                                            value.start
                                          ),
                                        end: convertToInternationalizedDateTimeToReadble(
                                          value.end
                                        ),
                                      });
                                    }}
                                    classNames={{
                                      label:
                                        "after:content-['*'] after:text-red-500 after:ml-1",
                                    }}
                                    isDisabled={!watch("discount.isDiscount")}
                                    isInvalid={
                                      !!errors?.discount?.duration?.start
                                    }
                                    errorMessage={
                                      errors?.discount?.duration?.start?.message
                                    }
                                  />
                                )}
                              />
                            )}
                          </div>

                          {/* MAX COUNT */}
                          <CustomNumberInput
                            label="Usage Limit/Maximum Discount Uses"
                            description="The maximum number of times this discount can be applied. Once this limit is reached, the discount will automatically expire. To keep it always available, set the value to 0."
                            placeholder="Enter count"
                            {...register("discount.maxCount")}
                            startContent={
                              <FaUser className="text-gray-400" size={14} />
                            }
                            isInvalid={!!errors.discount?.maxCount?.message}
                            errorMessage={errors.discount?.maxCount?.message}
                            isDisabled={!watch("discount.isDiscount")}
                            className="col-span-2"
                          />
                        </div>
                      </div>
                    </>
                  }
                />
              )}

              {/* INCLUDE */}
              <CustomCardWithHeader
                title="Service Includes"
                subtitle="Add your what are the service includes"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <div>
                      {fields.map((field, index) => (
                        <div
                          key={field.id}
                          className="flex gap-1 laptop:gap-5 flex-inline justify-center items-baseline space-y-2 "
                        >
                          <CustomInput
                            type="text"
                            placeholder="Enter include title"
                            {...register(`includes.${index}`)}
                            isInvalid={!!errors?.includes?.[index]?.message}
                            errorMessage={errors?.includes?.[index]?.message}
                          />

                          {fields.length > 1 && (
                            <div className="cursor-pointer">
                              <CustomButton
                                isIconOnly={true}
                                variant="light"
                                onPress={() => remove(index)}
                              >
                                <RiDeleteBin4Line
                                  size={20}
                                  className="text-red-400"
                                />
                              </CustomButton>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    <div className="mt-5 ">
                      <CustomButton
                        label="Add include"
                        startContent={
                          <IoIosAddCircleOutline
                            size={20}
                            className="text-green-600 cursor-pointer"
                          />
                        }
                        variant="flat"
                        color="success"
                        onPress={() => {
                          append();
                        }}
                      />
                    </div>
                  </div>
                }
              />

              {/*  ADDTIONAL SERVICE  */}
              <CustomCardWithHeader
                title="Addtional Service"
                subtitle="Add your additional service"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <div className="flex items-beaseline flex-initial gap-4 mb-3">
                      <p className="text-body-bold">
                        Add Addtional Service to Service
                      </p>
                      <Switch
                        isSelected={isAddtional}
                        onValueChange={setIsAddtional}
                        size="sm"
                      ></Switch>
                    </div>

                    <>
                      {addtionalServiceField.map((field, index) => (
                        <div
                          key={field.id}
                          // className="grid tablet:grid-cols-3 laptop:grid-cols-2 gap-3 mt-2 laptop:inline-flex laptop:justify-center laptop:items-baseline "
                          className="grid tablet:grid-cols-3 laptop:grid-cols-2 gap-3 mt-2 laptop:inline-flex laptop:justify-center laptop:items-baseline  w-full"
                        >
                          <div className="relative">
                            <label
                              htmlFor={`images-upload-${index}`}
                              className={`rounded-lg flex justify-center items-center cursor-pointer aspect-square tablet:max-w-[100px] tablet:max-h-[100px] laptop:w-[60px] laptop:h-[60px] 
    ${
      errors?.additionalService?.[index]?.images?.message
        ? "border border-red-500 bg-red-100"
        : "bg-gray-200 dark:bg-gray-800 border border-dashed border-gray-400"
    }`}
                            >
                              {watch(
                                `additionalService.${index}.images`
                              )?.[0] ? (
                                <>
                                  <img
                                    src={URL.createObjectURL(
                                      watch(
                                        `additionalService.${index}.images`
                                      )?.[0]
                                    )}
                                    alt="Preview"
                                    className="rounded-lg object-contain aspect-square w-full h-full relative"
                                  />

                                  <span
                                    className="absolute -top-2 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex justify-center items-center cursor-pointer"
                                    onClick={() =>
                                      setValue(
                                        `additionalService.${index}.images`,
                                        ""
                                      )
                                    }
                                  >
                                    <IoMdClose />
                                  </span>
                                </>
                              ) : (
                                <span className="text-gray-500 text-4xl tablet:text-sm">
                                  +
                                </span>
                              )}
                            </label>
                          </div>

                          <input
                            id={`images-upload-${index}`}
                            type="file"
                            accept="image/*"
                            multiple={false}
                            className="hidden"
                            disabled={!isAddtional}
                            {...register(
                              `additionalService.${index}.images` as const,
                              {
                                ...(isAddtional && {
                                  required: "Image is required",
                                }),
                              }
                            )}
                          />

                          {/* Name Input */}
                          <CustomInput
                            label="Name"
                            type="text"
                            placeholder="Enter title"
                            isDisabled={!isAddtional}
                            {...register(
                              `additionalService.${index}.serviceItem` as keyof IServiceSubmitProps
                            )}
                            isInvalid={
                              !!errors?.additionalService?.[index]?.serviceItem
                                ?.message
                            }
                            errorMessage={
                              errors?.additionalService?.[index]?.serviceItem
                                ?.message
                            }
                          />

                          {/* Price Input */}
                          <CustomNumberInput
                            label="Price"
                            placeholder="Enter Price"
                            isDisabled={!isAddtional}
                            startContent={
                              <BiDollar className="text-gray-400" />
                            }
                            {...register(
                              `additionalService.${index}.price` as keyof IServiceProps
                            )}
                            isInvalid={
                              !!errors?.additionalService?.[index]?.price
                                ?.message
                            }
                            errorMessage={
                              errors?.additionalService?.[index]?.price?.message
                            }
                          />

                          {/* Remove Button */}
                          {addtionalServiceField.length > 1 && (
                            <div className="cursor-pointer hidden laptop:block">
                              <CustomButton
                                isIconOnly={true}
                                variant="faded"
                                onPress={() => removeAdditionalService(index)}
                              >
                                <RiDeleteBin4Line
                                  size={20}
                                  className="text-red-400"
                                />
                              </CustomButton>
                            </div>
                          )}
                        </div>
                      ))}

                      {/* Add New Button */}
                      <div className="mt-5">
                        <CustomButton
                          label="Add service"
                          isDisabled={!isAddtional}
                          startContent={
                            <IoIosAddCircleOutline
                              size={20}
                              className="text-green-600 cursor-pointer"
                            />
                          }
                          variant="flat"
                          color="success"
                          onPress={() => {
                            appendAddtionalService({
                              id: Math.floor(Math.random() * 100).toString(),
                              images: "",
                              serviceItem: "",
                              price: 0,
                            });
                          }}
                        />
                      </div>
                    </>
                  </div>
                }
              />

              {/* SERVICE ORVERVIEW */}
              <CustomCardWithHeader
                title="Service Overview"
                subtitle="Add service overview"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <Controller
                      name="serviceOverview"
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <>
                          <TextEditor
                            value={field.value}
                            onChangeValue={(value) => {
                              field.onChange(value);
                            }}
                            isError={!!errors?.serviceOverview?.message}
                          />
                          {errors?.serviceOverview?.message && (
                            <p className="text-error mt-5">
                              {errors?.serviceOverview?.message}
                            </p>
                          )}
                        </>
                      )}
                    />
                  </div>
                }
              />

              {/* Gallary */}
              <CustomCardWithHeader
                title="Gallary"
                subtitle="Add service gallary"
                isHeaderDevider
                className="mt-5 mr-5"
                mainContent={
                  <div className="-mt-2">
                    <div className="gap-6 mb-5">
                      <Controller
                        name="gallery.0.serviceImages"
                        control={control}
                        defaultValue={[]} // not an object — this should match yup's expected type
                        render={({ field }) => (
                          <GallaryInput
                            value={field.value} // just an array of string | File
                            onChangeValue={(newVal) =>
                              field.onChange(newVal.images)
                            } // extract array only
                            error={errors.gallery?.[0]?.serviceImages?.message}
                          />
                        )}
                      />
                    </div>
                    {errors?.gallery?.[0]?.serviceImages?.message && (
                      <small className="text-danger ">
                        {errors.gallery?.[0]?.serviceImages?.message}
                      </small>
                    )}

                    <div className="mt-10">
                      <CustomInput
                        size="md"
                        label="Video Link"
                        description="Only YouTube link are supported."
                        type="text"
                        placeholder="https://www.example.com"
                        {...register("gallery.0.videoLink")}
                        startContent={<BsLink45Deg />}
                        isInvalid={!!errors?.gallery?.[0]?.videoLink}
                        errorMessage={errors?.gallery?.[0]?.videoLink?.message}
                      />
                    </div>
                  </div>
                }
              />
            </div>

            {/* RIGHT SIDE */}
            <div>
              <CustomCardWithHeader
                title="Availability"
                subtitle="Set the availability of your service"
                isHeaderDevider
                mainContent={
                  <div className="-mt-2">
                    <AvailabilitySection />
                  </div>
                }
              />

              {/* LOCATION */}
              <CustomCardWithHeader
                title="Service Location"
                subtitle="Add the service location"
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <LocationInputs />
                  </div>
                }
              />

              {/* FAQ */}
              <CustomCardWithHeader
                title="FAQ"
                subtitle="Add frequently asked questions about your service"
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <div>
                      <div className="flex items-baseline flex-initial gap-4">
                        <p className="text-md font-medium">Enable FAQ</p>
                        <Switch
                          isSelected={isFaq}
                          onValueChange={setIsFaq}
                          size="sm"
                          className="mb-5"
                        ></Switch>
                      </div>
                    </div>

                    <div className="gap-6 ">
                      {faqField.map((field, index) => (
                        <div
                          key={field.id}
                          className="grid tablet:grid-cols-2 gap-3  laptop:inline-flex laptop:justify-center laptop:items-center w-full mt-3 first:mt-0"
                        >
                          <CustomInput
                            label="Question"
                            type="text"
                            placeholder="Enter your question"
                            isDisabled={!isFaq}
                            {...register(`faq.${index}.question` as const)}
                            isInvalid={!!errors.faq?.[index]?.question?.message}
                            errorMessage={
                              errors.faq?.[index]?.question?.message
                            }
                          />

                          <CustomInput
                            label="Answer"
                            type="text"
                            placeholder="Enter your answer"
                            isDisabled={!isFaq}
                            {...register(`faq.${index}.answer` as const)}
                            isInvalid={!!errors.faq?.[index]?.answer?.message}
                            errorMessage={errors.faq?.[index]?.answer?.message}
                          />

                          {faqField.length > 1 && (
                            <>
                              <div className="mt-6 cursor-pointer hidden laptop:block">
                                <CustomButton
                                  isIconOnly={true}
                                  isDisabled={!isFaq}
                                  variant="light"
                                  onPress={() => removeFaq(index)}
                                >
                                  <RiDeleteBin4Line
                                    size={20}
                                    className="text-red-400"
                                  />
                                </CustomButton>
                              </div>

                              <div className=" mt-2  laptop:hidden">
                                <CustomButton
                                  label="Remove"
                                  color="danger"
                                  variant="flat"
                                  fullWidth={true}
                                  isDisabled={!isFaq}
                                  onPress={() => removeFaq(index)}
                                  startContent={
                                    <BsDashCircle
                                      size={16}
                                      className="text-red-600 cursor-pointer"
                                    />
                                  }
                                />
                              </div>
                            </>
                          )}
                        </div>
                      ))}

                      <div className="mt-5">
                        <CustomButton
                          label="Add FAQ"
                          color="success"
                          isDisabled={!isFaq}
                          startContent={
                            <IoIosAddCircleOutline
                              size={20}
                              className="text-green-600 cursor-pointer"
                            />
                          }
                          variant="flat"
                          onPress={() =>
                            appendFaq({
                              question: "",
                              answer: "",
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>
                }
              />

              {/* SEO */}
              <CustomCardWithHeader
                title="SEO"
                subtitle="Add SEO details for your service"
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <SeoSection />
                  </div>
                }
              />

              {/* Active Service */}
              {/* SEO */}
              <CustomCardWithHeader
                title="Service Status"
                subtitle="Control whether this service is visible to customers."
                isHeaderDevider
                className="mt-5"
                mainContent={
                  <div className="-mt-2">
                    <CustomCheckbox
                      label="Make Service Available"
                      {...register("isActive")}
                      className="mb-2 -mt-2"
                    />
                  </div>
                }
              />
            </div>
          </div>

          <div className="flex flex-initial justify-end items-end my-1 gap-5">
            <CustomButton
              label="Clear"
              type="reset"
              color="danger"
              variant="flat"
            />
            <CustomButton
              label="Create Service"
              type="submit"
              color="primary"
              // isDisabled={!isVerifyAccount ? true : false}
            />
          </div>
        </form>
      </FormProvider>
    </>
  );
};

export default AddService1;

const maxCountInfo = (maxCount: number) => (
  <div className="text-sm space-y-2">
    <div className="font-semibold text-base text-gray-800 dark:text-white">
      Max Count Information
    </div>

    <div className="bg-gray-50 dark:bg-sideBarBackground dark:border-gray-600 p-2 rounded-md border text-gray-700 dark:text-gray-100">
      <p>
        Maximum number of users that can claim the discount:
        <span className="font-semibold">
          {" "}
          {maxCount === 0 ? "Unlimited" : maxCount}
        </span>
      </p>
      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
        {maxCount === 0
          ? "All users can claim the discount without limit."
          : `Only up to ${maxCount} users can claim the discount.`}
      </p>
    </div>
  </div>
);
