import {
  addToast,
  Card,
  CardBody,
  Modal,
  Modal<PERSON>ody,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  useDisclosure,
} from "@heroui/react";
import { MdAddCircleOutline } from "react-icons/md";
import CustomButton from "../../components/ui/CustomButton";
import CustomAutocomplete from "../../components/ui/CustomAutocomplete";
import { useForm, Controller, FormProvider } from "react-hook-form";
import { IPromotionSumbit, IServiceGet } from "../../types";
import { useFetchAllServiceByProvider } from "../../hooks/queries/useFetchData";
import { yupResolver } from "@hookform/resolvers/yup";
import { discountAddDefaultValue } from "../../utils/defaultValue";
import DiscountSection from "./DiscountSection";
import { useEffect, useState } from "react";
import CustomChip from "./CustomChip";
import CustomDivider from "./CustomDivider";
import { useUpdateDiscount } from "../../hooks/mutations/useUpdateData";
import { discountSchema } from "../../validation/discountSchema";
import { useCreateDiscountMutation } from "../../hooks/mutations/usePostData";

const AddDiscountModal = () => {
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
  const [selectService, setSelectService] = useState<IServiceGet>();
  const [packages, setPackages] = useState<any[]>([]);
  const [selectPackage, setSelectPackage] = useState<any>();

  const { mutate: updateDiscountMutate, isPending: isUpdatePending } =
    useUpdateDiscount();
  const { mutate: createDiscountMutate, isPending: isCreatePending } =
    useCreateDiscountMutation();

  const form = useForm<IPromotionSumbit>({
    defaultValues: discountAddDefaultValue,
    shouldUnregister: true,
    resolver: yupResolver(discountSchema),
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const {
    control,
    setValue,
    watch,
    reset,
    resetField,
    formState: { errors },
  } = form;

  console.log("PROMOTION ERROR: ", errors);

  const { data: apiData } = useFetchAllServiceByProvider(1, 20);

  const service: any[] = apiData?.services || [];

  console.log("Watch:", watch());
  console.log("packageId:", watch("packageId"));
  const serviceId = watch("serviceId");
  const packageId = watch("packageId");

  // Get service name id
  const convertService = (service: any[]) => {
    if (!service) return [];
    return service.map((item) => ({
      label: item.serviceTitle,
      id: item.serviceId,
    }));
  };

  //GET PACKAGE BY SERVICE ID
  useEffect(() => {
    const matchServiceFuction = () => {
      setPackages([]);
      setSelectPackage(null);
      setValue("discountId", "");

      const matchedService = service.find(
        (item: any) => item.serviceId === serviceId
      );
      console.log("Matched service: ", matchedService);
      if (matchedService) {
        setPackages([]);

        setSelectService(matchedService);

        setValue("isPackage", matchedService?.isPackage || false);
        setValue("discount.isPackage", matchedService?.isPackage || false);
        setValue("serviceId", matchedService?.serviceId);

        if (matchedService?.packages?.length > 0) {
          setPackages(matchedService?.packages);
        } else {
          setPackages([]);
          if (matchedService?.isDiscount) {
            setValue("discount.amount", matchedService?.discount?.amount);
            setValue("discount.promoCode", matchedService?.discount?.promoCode);
            setValue("discount.maxCount", matchedService?.discount?.maxCount);
            setValue("discountId", matchedService?.discount?.discountId);
            setValue(
              "discount.discountType",
              matchedService?.discount?.discountType || "general-discount"
            );
            setValue(
              "discount.valueType",
              matchedService?.discount?.valueType || "percentage"
            );
            setValue(
              "discount.durationType",
              matchedService?.discount?.durationType || "life-time"
            );
            setValue(
              "discount.duration.end",
              matchedService?.discount?.duration?.end
            );
            setValue(
              "discount.duration.start",
              matchedService?.discount?.duration?.start
            );
          } else {
            setValue(
              "discount.discountType",
              matchedService?.discount?.discountType || "general-discount"
            );
            setValue(
              "discount.valueType",
              matchedService?.discount?.valueType || "percentage"
            );
            setValue(
              "discount.durationType",
              matchedService?.discount?.durationType || "life-time"
            );
          }
        }
      }
    };

    matchServiceFuction();
  }, [serviceId, service]);

  //GET PACKAGR BY PACKAGE IG
  useEffect(() => {
    setSelectPackage(null);

    const matchedPackage = selectService?.packages?.find(
      (item: any) => item.packageId === packageId
    );

    if (matchedPackage) {
      setSelectPackage(matchedPackage);
      setValue("discount.amount", matchedPackage?.discount?.amount || 0);
      setValue("discount.maxCount", matchedPackage?.discount?.maxCount || 0);
      setValue(
        "discount.discountType",
        matchedPackage?.discount?.discountType || "general-discount"
      );
      setValue(
        "discount.valueType",
        matchedPackage?.discount?.valueType || "percentage"
      );
      setValue(
        "discount.durationType",
        matchedPackage?.discount?.durationType || "life-time"
      );
      setValue(
        "discount.duration.end",
        matchedPackage?.discount?.duration?.end
      );
      setValue(
        "discount.duration.start",
        matchedPackage?.discount?.duration?.start || ""
      );

      setValue("discountId", matchedPackage?.discount?.discountId);
    }

    console.log("Matched package: ", matchedPackage);
    console.log("Matched selectService: ", selectService);
  }, [packageId]);

  console.log("Filter : ", selectService);
  console.log("packages : ", packages);
  console.log("ALL PKG DATA : ", service);
  console.log("Select pkg: ", selectPackage);

  // SUBMIT DATA
  const onSubmit = async (data: any) => {
    try {
      console.log(" watch Final data: ", data);

      if (!serviceId) return;

      const discountId = data?.discountId || null;

      if (discountId) {
        const payload: any = {
          serviceId: serviceId,
          serviceName: selectService?.serviceTitle,
          packageId: selectPackage?.packageId || null,
          packageName: selectPackage?.packageName || null,
          isDiscount: true,
          discountType: data?.discount?.discountType,
          valueType: data?.discount?.valueType,
          durationType: data?.discount?.durationType,
          amount: Number(data?.discount?.amount),
          duration: {
            start: data?.discount?.duration?.start,
            end: data?.discount?.duration?.end || "",
          },
          promoCode: data?.discount?.promoCode || "",
          maxCount: Number(data?.discount?.maxCount) || 0,
        };

        console.log("watch Final UPLOAD payload: ", payload);

        await updateDiscountMutate({ id: discountId, data: payload });
        onClose();
        reset();
      } else {
        const payload: any = {
          serviceId: serviceId,
          serviceName: selectService?.serviceTitle,
          packageId: selectPackage?.packageId || null,
          packageName: selectPackage?.packageName || null,
          isDiscount: true,
          discountType: data?.discount?.discountType,
          valueType: data?.discount?.valueType,
          durationType: data?.discount?.durationType,
          amount: Number(data?.discount?.amount),
          duration: {
            start: data?.discount?.duration?.start,
            end: data?.discount?.duration?.end || "",
          },
          promoCode: data?.discount?.promoCode || "",
          maxCount: Number(data?.discount?.maxCount) || 0,
        };

        console.log("watch Final CREATE payload: ", payload);

        await createDiscountMutate(payload);
        onClose();
        reset();
      }

      // await mutate({ id: discountId, payload });
    } catch (error) {
      console.error("Final error: ", data);
      addToast({
        title: "Error",
        description:
          error?.message ||
          "Something went wrong while adding or updating discount",
        radius: "md",
        color: "danger",
      });
    }
  };

  const removeDiscount = async (discountId?: string) => {
    if (!discountId) return;
    try {
      const payload = {
        IsActive: false,
      };
      await updateDiscountMutate({ id: discountId, data: payload });
      onClose();
    } catch (error) {
      console.error("Error: ", error);
      addToast({
        title: "Error",
        description: error?.message || "Something went wrong",
        radius: "md",
        color: "danger",
      });
    }
  };

  return (
    <>
      <CustomButton
        label="Add Discount"
        type="button"
        size="sm"
        color="primary"
        startContent={<MdAddCircleOutline size={20} />}
        onPress={onOpen}
        className="-mt-6 sm:mt-0"
      />
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="5xl">
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <ModalContent>
              {(onClose) => (
                <>
                  <ModalHeader className="flex flex-col gap-1">
                    Add Promotion
                  </ModalHeader>
                  <ModalBody>
                    <div className="grid grid-cols-2 gap-5">
                      <div className="grid max-w-[550px] gap-5 mt-2 ">
                        <Controller
                          name="serviceId"
                          control={control}
                          defaultValue=""
                          render={({ field }) => (
                            <>
                              <CustomAutocomplete
                                label="Select Service"
                                placeholder="Select service"
                                defaultItems={convertService(service)}
                                width="none"
                                onSelectionChange={(val) => {
                                  field.onChange(val);
                                }}
                                isInvalid={!!errors?.serviceId}
                                errorMessage={errors?.serviceId?.message}
                              />
                            </>
                          )}
                        />
                      </div>
                    </div>
                    {/* <DiscountSection /> */}
                    {packages.length > 0 && (
                      <div>
                        <p className="text-body mt-3 ">Package</p>
                        <CustomDivider />
                        <div className="grid grid-cols-1 gap-2 mt-3">
                          {packages.map((p, index: number) => (
                            <>
                              <Controller
                                name="packageId"
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                  <>
                                    <Card
                                      key={index}
                                      radius="sm"
                                      className={`
    border cursor-pointer shadow-sm hover-primary-gradient
    ${packageId === p?.packageId ? "bg-secondary/40" : ""}
    ${errors.packageId ? "border-red-500" : "border-secondary/30"}
  `}
                                    >
                                      <CardBody
                                        onClick={() => {
                                          field.onChange(p?.packageId);
                                        }}
                                      >
                                        <div className="flex flex-initial items-center justify-between ">
                                          <div className="flex flex-initial items-center gap-4">
                                            <p className="text-body-bold">
                                              {p?.packageName}
                                            </p>
                                            {p?.isDiscount && (
                                              <CustomChip
                                                label="Discount Appyled"
                                                color="success"
                                                variant="bordered"
                                              />
                                            )}
                                          </div>
                                          <p className="text-body-bold">
                                            ${p?.price}
                                          </p>
                                        </div>
                                      </CardBody>
                                    </Card>
                                  </>
                                )}
                              />
                            </>
                          ))}
                        </div>
                      </div>
                    )}

                    <div>
                      <p className="text-body mt-3 ">Discount Section</p>
                      <CustomDivider />
                      <DiscountSection />

                      {watch("discountId") && (
                        <div className="flex items-start mt-5">
                          <CustomButton
                            color="danger"
                            variant="faded"
                            onPress={() => removeDiscount(watch("discountId"))}
                            label="Remove discount"
                          />
                        </div>
                      )}
                    </div>
                  </ModalBody>
                  <ModalFooter>
                    <CustomButton
                      color="danger"
                      variant="light"
                      onPress={() => {
                        onClose();
                        reset();
                      }}
                      label="Close"
                    />
                    <CustomButton
                      type="submit"
                      label="Submit"
                      color="primary"
                      // isLoading={isPending}
                    />
                  </ModalFooter>
                </>
              )}
            </ModalContent>
          </form>
        </FormProvider>
      </Modal>
    </>
  );
};

export default AddDiscountModal;
