import { useState } from "react";
import CustomButton from "../../components/ui/CustomButton";

import {
  Modal,
  <PERSON>dalContent,
  ModalHeader,
  ModalBody,
  useDisclosure,
  addToast,
} from "@heroui/react";
import VerifyIndividual from "./VerifyIndividual";
import VerifyOrganization from "./VerifyOrganization";
import { FormProvider, useForm, useFormContext } from "react-hook-form";
import { providerDefaultValue } from "../../utils/defaultValue";
import {
  IProviderInformationProps,
  ProviderVerifySubmitProps,
} from "../../types";
import { providerSchema } from "../../validation/providerSchema";
import { yupResolver } from "@hookform/resolvers/yup";
import { useUploadProviderVerfiyDocument } from "../../hooks/mutations/usePostData";
import { useUpdateUserprofile } from "../../hooks/mutations/useUpdateData";
import { useFetchUserDetailsById } from "../../hooks/queries/useFetchData";
import { useAuth } from "react-oidc-context";
import LoadingModalUi from "../../components/ui/LoadingModalUi";

const ProviderVerifyModal = () => {
  const auth = useAuth();
  const providerId = auth.user?.profile?.preferred_username;
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
  const [loading, setLoading] = useState(false);

  const { data: providerDetails } = useFetchUserDetailsById(providerId);
  const { mutateAsync } = useUploadProviderVerfiyDocument();
  const { mutateAsync: mutateGeneralData } = useUpdateUserprofile();

  const methods = useForm<ProviderVerifySubmitProps>({
    defaultValues: providerDefaultValue,
    shouldUnregister: true,
    resolver: yupResolver(providerSchema),
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const {
    watch,
    formState: { errors },
  } = methods;

  const onSubmit = async (data: ProviderVerifySubmitProps) => {
    setLoading(true);
    try {
      //FIXME: Change Get UserId
      const user = providerDetails?.user;
      const userId = user.userId;

      if (!userId) {
        addToast({
          title: "User Id not found",
          description:
            "Required parameters are missing or invalid. Please contact support if the issue persists.",
          radius: "md",
          color: "danger",
        });
        throw new Error("");
      }

      const generalPayload = {
        isBuniness: data.isBuniness ? true : false,
        firstName: data.firstName,
        middleName: data.middleName,
        lastName: data.lastName,
        dateOfBirth: data.dateOfBirth,
        address: {
          addressLine1: data.address.addressLine1,
          country: data.address.country,
          state: data.address.state,
          city: data.address.city,
          postalCode: data.address.postalCode,
        },
      };

      const doc = {
        documentNo: data.documents.documentNo,
        documentType: data.documents.documentType,
        front: data.documents.front,
        back: data.documents.back,
        issueDate: data.documents.issueDate,
        expiryDate: data.documents.issueDate,
      };

      console.log("General Payload: ", generalPayload);
      console.log("Document Payload: ", doc);

      await mutateGeneralData({ id: userId, data: generalPayload });
      await mutateAsync({ id: userId, data: doc });

      addToast({
        title: "Update Success",
        description: "Profile updated successfully",
        radius: "md",
        color: "success",
      });

      // onClose();
    } catch (error) {
      console.error("Error submit provider verify: ", error);
    } finally {
      setLoading(false);
    }
  };

  console.log("Watch ALL date: ", watch());
  console.log("Error: ", errors);

  return (
    <>
      <CustomButton
        label="Verify"
        onPress={onOpen}
        color="warning"
        variant="flat"
      />
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        size="5xl"
        scrollBehavior="inside"
        className="p-5"
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader>Provider Verification</ModalHeader>
              <ModalBody>
                <FormProvider {...methods}>
                  <form onSubmit={methods.handleSubmit(onSubmit)}>
                    {!loading ? (
                      <TabUi />
                    ) : (
                      <LoadingModalUi label={"Uploading..."} />
                    )}
                    {/* <TabUi /> */}
                  </form>
                </FormProvider>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

const tabs = [
  {
    id: "Individual",
    label: "Individual",
    content: <VerifyIndividual />,
  },
  {
    id: "Business",
    label: "Business",
    content: <VerifyOrganization />,
  },
];

const TabUi = () => {
  const { setValue } = useFormContext<IProviderInformationProps>();
  const [selectedTabId, setSelectedTabId] = useState("Individual");

  const handleTabChange = (tabId: string) => {
    setSelectedTabId(tabId);
    setValue("isBuniness", tabId === "Business");
  };

  return (
    <div>
      <div className="flex  space-x-4 border-b-1 dark:border-gray-600 mb-4">
        {tabs.map((tab) => (
          <button
            type="button"
            key={tab.id}
            className={`px-10 py-2 -mb-px font-medium border-b-2 min-w-md  ${
              selectedTabId === tab.id
                ? "border-primary text-primary"
                : "border-transparent text-gray-500 hover:text-primary"
            }`}
            onClick={() => handleTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab content */}
      <div className="mt-5">
        {tabs.find((tab) => tab.id === selectedTabId)?.content}
      </div>
    </div>
  );
};

export default ProviderVerifyModal;
