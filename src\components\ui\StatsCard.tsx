import { Card, CardBody } from "@heroui/react";
import { ReactNode } from "react";
import SmallLoadingSpinner from "./SmallLoadingSpinner";

interface ReusableCardProps {
  title?: string;
  icon?: ReactNode;
  value?: string | number;
  shadow?: "none" | "sm" | "md" | "lg";
  isHoverEffect?: boolean;
  isLoading?: boolean;
  className?: string;
}

const StatsCard = ({
  title,
  icon,
  value,
  shadow,
  isHoverEffect,
  isLoading,
  className = "",
}: ReusableCardProps) => {
  return (
    <Card
      shadow="none"
      className={`border-1 border-secondary/70  border-b-3 
          ${shadow !== "none" ? `shadow-${shadow}` : ""} 
    ${
      isHoverEffect
        ? "relative flex  items-center justify-center overflow-hidden transition-all hover:scale-105 before:absolute before:h-0 before:w-0 before:rounded-full before:bg-secondary/10 before:duration-300 before:ease-out hover:shadow-md hover:before:h-56 hover:before:w-56"
        : // "relative transition-all before:absolute before:bottom-0 before:left-0 before:top-0 before:z-0 before:h-full before:w-0 before:bg-[linear-gradient(90deg,hsla(196,85%,95%,1)_0%,hsla(0,0%,100%,1)_100%)] before:transition-all before:duration-500  hover:before:left-0 hover:before:w-full"
          ""
    } 
    ${className}`}
    >
      <CardBody className="flex justify-center items-center">
        <div className="flex flex-col items-center justify-center gap-1">
          {icon}
          <p className="text-heading  ">
            {isLoading ? <SmallLoadingSpinner /> : value}
          </p>
          <p className="text-body-bold ">{title}</p>
        </div>
      </CardBody>
    </Card>
  );
};

export default StatsCard;
// bg-[linear-gradient(90deg,hsla(196,100%,95%,1)_0%,hsla(0,0%,100%,1)_100%)]
