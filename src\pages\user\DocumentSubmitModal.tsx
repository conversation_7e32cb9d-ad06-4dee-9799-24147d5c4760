import React from "react";
import CustomDivider from "../../components/ui/CustomDivider";
import {
  Autocomplete,
  AutocompleteItem,
  Button,
  Card,
  Modal,
  ModalBody,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  useDisclosure,
} from "@heroui/react";
import { individualDocumentType } from "../../data/sampleData";
import CustomInput from "../../components/ui/CustomInput";
import DocumentUplaodFrontSide from "../../components/ui/DocumentUploadBackSide";
import { IoCloudUploadOutline } from "react-icons/io5";
import DocumentUplaodBackSide from "../../components/ui/DocumentUploadBackSide";

const DocumentSubmitModal = () => {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  return (
    <>
      <Button onPress={onOpen}>Open Modal</Button>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="5xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                Document Upload
              </ModalHeader>
              <ModalBody>
                <Card radius="none" shadow="none" className="">
                  <div>
                    <div>
                      <div className="grid md:grid-cols-2 gap-5 mt-5">
                        {/* <Controller
                      name="documents.type"
                      control={control}
                      render={({ field, fieldState }) => ( */}
                        <Autocomplete
                          //   {...field}
                          //   onSelectionChange={(key) => field.onChange(key)}
                          //   selectedKey={field.value}
                          radius="sm"
                          labelPlacement="outside"
                          size="md"
                          variant="bordered"
                          label="Document Type"
                          placeholder="Select a document type"
                          //   isInvalid={!!fieldState.error}
                          //   errorMessage={fieldState.error?.message}
                        >
                          {individualDocumentType.map((s) => (
                            <AutocompleteItem key={s.key}>
                              {s.label}
                            </AutocompleteItem>
                          ))}
                        </Autocomplete>
                        {/* )} */}
                        {/* /> */}

                        <CustomInput
                          //   label={documnetLabel}
                          label="Document Number"
                          radius="sm"
                          variant="bordered"
                          size="md"
                          placeholder="Enter number"
                          type="text"
                          //   {...register("documents.documentNo")}
                          //   isInvalid={!!errors?.documents?.documentNo}
                          //   errorMessage={errors?.documents?.documentNo?.message}
                        />
                      </div>

                      <div className="grid xl:grid-cols-2 gap-5 my-5">
                        <div>
                          <p className="text-subtitle1 my-3">Front Side</p>
                          {/* <Controller
                        name="documents.front"
                        control={control}
                        render={({ field }) => ( */}
                          <DocumentUplaodFrontSide
                            title="Upload Front Side"
                            icon={<IoCloudUploadOutline size={25} />}
                            // value={field.value}
                            // onChange={field.onChange}
                            // error={!!errors?.documents?.front}
                          />
                          {/* )}
                      /> */}

                          {/* <small className="text-error">
                        {errors?.documents?.front?.message}
                      </small> */}

                          <small className="flex flex-col text-gray-500">
                            Note: Make sure the entire front side is visible,
                            clear, and well-lit.
                          </small>
                        </div>

                        {/* TODO: If select passport hide backside */}
                        <div>
                          <p className="text-subtitle1 my-3">Back Side</p>
                          {/* <Controller
                        name="documents.back"
                        control={control}
                        render={({ field }) => ( */}
                          <DocumentUplaodBackSide
                            title="Uplaod Back Side"
                            icon={<IoCloudUploadOutline size={25} />}
                            // value={field.value}
                            // onChange={field.onChange}
                            // error={!!errors?.documents?.back}
                          />
                          {/* )}
                      /> */}

                          {/* <small className="text-error">
                        {errors?.documents?.back?.message}
                      </small>
     */}
                          <small className="flex flex-col text-gray-500">
                            Note: Ensure the back side is fully visible and all
                            text is readable.
                          </small>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-5 mt-6"></div>
                  </div>
                </Card>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default DocumentSubmitModal;
